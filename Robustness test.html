<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en"><head>

<meta charset="utf-8">
<meta name="generator" content="quarto-1.6.39">

<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">


<title>Robustness Check</title>
<style>
code{white-space: pre-wrap;}
span.smallcaps{font-variant: small-caps;}
div.columns{display: flex; gap: min(4vw, 1.5em);}
div.column{flex: auto; overflow-x: auto;}
div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
ul.task-list{list-style: none;}
ul.task-list li input[type="checkbox"] {
  width: 0.8em;
  margin: 0 0.8em 0.2em -1em; /* quarto-specific, see https://github.com/quarto-dev/quarto-cli/issues/4556 */ 
  vertical-align: middle;
}
/* CSS for syntax highlighting */
pre > code.sourceCode { white-space: pre; position: relative; }
pre > code.sourceCode > span { line-height: 1.25; }
pre > code.sourceCode > span:empty { height: 1.2em; }
.sourceCode { overflow: visible; }
code.sourceCode > span { color: inherit; text-decoration: inherit; }
div.sourceCode { margin: 1em 0; }
pre.sourceCode { margin: 0; }
@media screen {
div.sourceCode { overflow: auto; }
}
@media print {
pre > code.sourceCode { white-space: pre-wrap; }
pre > code.sourceCode > span { display: inline-block; text-indent: -5em; padding-left: 5em; }
}
pre.numberSource code
  { counter-reset: source-line 0; }
pre.numberSource code > span
  { position: relative; left: -4em; counter-increment: source-line; }
pre.numberSource code > span > a:first-child::before
  { content: counter(source-line);
    position: relative; left: -1em; text-align: right; vertical-align: baseline;
    border: none; display: inline-block;
    -webkit-touch-callout: none; -webkit-user-select: none;
    -khtml-user-select: none; -moz-user-select: none;
    -ms-user-select: none; user-select: none;
    padding: 0 4px; width: 4em;
  }
pre.numberSource { margin-left: 3em;  padding-left: 4px; }
div.sourceCode
  {   }
@media screen {
pre > code.sourceCode > span > a:first-child::before { text-decoration: underline; }
}
</style>


<script src="Robustness test_files/libs/clipboard/clipboard.min.js"></script>
<script src="Robustness test_files/libs/quarto-html/quarto.js"></script>
<script src="Robustness test_files/libs/quarto-html/popper.min.js"></script>
<script src="Robustness test_files/libs/quarto-html/tippy.umd.min.js"></script>
<script src="Robustness test_files/libs/quarto-html/anchor.min.js"></script>
<link href="Robustness test_files/libs/quarto-html/tippy.css" rel="stylesheet">
<link href="Robustness test_files/libs/quarto-html/quarto-syntax-highlighting-e26003cea8cd680ca0c55a263523d882.css" rel="stylesheet" id="quarto-text-highlighting-styles">
<script src="Robustness test_files/libs/bootstrap/bootstrap.min.js"></script>
<link href="Robustness test_files/libs/bootstrap/bootstrap-icons.css" rel="stylesheet">
<link href="Robustness test_files/libs/bootstrap/bootstrap-8a79a254b8e706d3c925cde0a310d4f0.min.css" rel="stylesheet" append-hash="true" id="quarto-bootstrap" data-mode="light">


</head>

<body class="fullcontent">

<div id="quarto-content" class="page-columns page-rows-contents page-layout-article">

<main class="content" id="quarto-document-content">

<header id="title-block-header" class="quarto-title-block default">
<div class="quarto-title">
<h1 class="title">Robustness Check</h1>
</div>



<div class="quarto-title-meta">

    
  
    
  </div>
  


</header>


<section id="package-installation-and-setup" class="level1">
<h1>Package Installation and Setup</h1>
<div class="cell">
<div class="sourceCode cell-code" id="cb1"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb1-1"><a href="#cb1-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Load the Organization &amp; Environment style function</span></span>
<span id="cb1-2"><a href="#cb1-2" aria-hidden="true" tabindex="-1"></a><span class="fu">source</span>(<span class="st">"modelsummary_OE_style.R"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>
Attaching package: 'kableExtra'</code></pre>
</div>
<div class="cell-output cell-output-stderr">
<pre><code>The following objects are masked from 'package:flextable':

    as_image, footnote</code></pre>
</div>
<div class="cell-output cell-output-stderr">
<pre><code>
Attaching package: 'dplyr'</code></pre>
</div>
<div class="cell-output cell-output-stderr">
<pre><code>The following object is masked from 'package:kableExtra':

    group_rows</code></pre>
</div>
<div class="cell-output cell-output-stderr">
<pre><code>The following objects are masked from 'package:plm':

    between, lag, lead</code></pre>
</div>
<div class="cell-output cell-output-stderr">
<pre><code>The following objects are masked from 'package:stats':

    filter, lag</code></pre>
</div>
<div class="cell-output cell-output-stderr">
<pre><code>The following objects are masked from 'package:base':

    intersect, setdiff, setequal, union</code></pre>
</div>
</div>
</section>
<section id="data-loading" class="level1">
<h1>Data loading</h1>
<div class="cell">
<div class="sourceCode cell-code" id="cb9"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb9-1"><a href="#cb9-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Load the data file</span></span>
<span id="cb9-2"><a href="#cb9-2" aria-hidden="true" tabindex="-1"></a><span class="fu">load</span>(<span class="st">"dta1_20240903.RData"</span>)</span>
<span id="cb9-3"><a href="#cb9-3" aria-hidden="true" tabindex="-1"></a><span class="co"># You can add a quick check to confirm the data loaded correctly</span></span>
<span id="cb9-4"><a href="#cb9-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-5"><a href="#cb9-5" aria-hidden="true" tabindex="-1"></a><span class="co"># 重新计算Age变量，使其随年份变化</span></span>
<span id="cb9-6"><a href="#cb9-6" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(lubridate)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>
Attaching package: 'lubridate'</code></pre>
</div>
<div class="cell-output cell-output-stderr">
<pre><code>The following objects are masked from 'package:base':

    date, intersect, setdiff, union</code></pre>
</div>
<div class="sourceCode cell-code" id="cb12"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb12-1"><a href="#cb12-1" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(dplyr)</span>
<span id="cb12-2"><a href="#cb12-2" aria-hidden="true" tabindex="-1"></a>dta1 <span class="ot">&lt;-</span> dta1 <span class="sc">%&gt;%</span></span>
<span id="cb12-3"><a href="#cb12-3" aria-hidden="true" tabindex="-1"></a>  <span class="fu">mutate</span>(</span>
<span id="cb12-4"><a href="#cb12-4" aria-hidden="true" tabindex="-1"></a>    <span class="at">EndYear =</span> <span class="fu">ymd</span>(EndYear),</span>
<span id="cb12-5"><a href="#cb12-5" aria-hidden="true" tabindex="-1"></a>    <span class="at">Year =</span> <span class="fu">year</span>(EndYear),</span>
<span id="cb12-6"><a href="#cb12-6" aria-hidden="true" tabindex="-1"></a>    <span class="at">Age =</span> Year <span class="sc">-</span> EstablishYear</span>
<span id="cb12-7"><a href="#cb12-7" aria-hidden="true" tabindex="-1"></a>  )</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</div>
</section>
<section id="robustness-test" class="level1">
<h1>Robustness test</h1>
<section id="short-term-impact-of-inspection" class="level2">
<h2 class="anchored" data-anchor-id="short-term-impact-of-inspection">Short-term Impact of Inspection</h2>
<p>Inspected 后三年标记为1, inspection当年标记为0 We assign a code of ‘1’ to firms within the three-year period following their initial inspection to assess the short-term effects of regulatory pressure. Table B2. Short-term Impact of Inspection</p>
<div class="cell">
<div class="sourceCode cell-code" id="cb13"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb13-1"><a href="#cb13-1" aria-hidden="true" tabindex="-1"></a><span class="co"># # 创建一个新的数据框，只包含所需的列</span></span>
<span id="cb13-2"><a href="#cb13-2" aria-hidden="true" tabindex="-1"></a><span class="co"># new_df &lt;- select(dta1, Symbol, EndYear, inspection_year, first_inspection, after_first_inspection, extended_inspection)</span></span>
<span id="cb13-3"><a href="#cb13-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb13-4"><a href="#cb13-4" aria-hidden="true" tabindex="-1"></a><span class="co"># na_rows &lt;- dta1 %&gt;% filter(is.na(first_inspection))</span></span>
<span id="cb13-5"><a href="#cb13-5" aria-hidden="true" tabindex="-1"></a><span class="co"># new_df &lt;- select(na_rows, Symbol, EndYear, inspection_year, first_inspection, after_first_inspection, extended_inspection)</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</div>
<section id="p3-connection-continuous" class="level3">
<h3 class="anchored" data-anchor-id="p3-connection-continuous">P3 connection (continuous)</h3>
<div class="cell">
<div class="sourceCode cell-code" id="cb14"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb14-1"><a href="#cb14-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 加载必要的库</span></span>
<span id="cb14-2"><a href="#cb14-2" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(lubridate)</span>
<span id="cb14-3"><a href="#cb14-3" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(dplyr)</span>
<span id="cb14-4"><a href="#cb14-4" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(tidyr)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>
Attaching package: 'tidyr'</code></pre>
</div>
<div class="cell-output cell-output-stderr">
<pre><code>The following objects are masked from 'package:Matrix':

    expand, pack, unpack</code></pre>
</div>
<div class="sourceCode cell-code" id="cb17"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb17-1"><a href="#cb17-1" aria-hidden="true" tabindex="-1"></a>dta3 <span class="ot">=</span> dta1</span>
<span id="cb17-2"><a href="#cb17-2" aria-hidden="true" tabindex="-1"></a>dta3 <span class="ot">&lt;-</span> dta3 <span class="sc">%&gt;%</span></span>
<span id="cb17-3"><a href="#cb17-3" aria-hidden="true" tabindex="-1"></a>  <span class="co"># 将 Endyear 转换为日期格式</span></span>
<span id="cb17-4"><a href="#cb17-4" aria-hidden="true" tabindex="-1"></a>  <span class="fu">mutate</span>(<span class="at">EndYear =</span> <span class="fu">ymd</span>(EndYear)) <span class="sc">%&gt;%</span></span>
<span id="cb17-5"><a href="#cb17-5" aria-hidden="true" tabindex="-1"></a>  <span class="co"># 提取年份并存储在 new_year 变量中</span></span>
<span id="cb17-6"><a href="#cb17-6" aria-hidden="true" tabindex="-1"></a>  <span class="fu">mutate</span>(<span class="at">Year =</span> <span class="fu">year</span>(EndYear)) <span class="sc">%&gt;%</span></span>
<span id="cb17-7"><a href="#cb17-7" aria-hidden="true" tabindex="-1"></a>  <span class="co"># 重新计算Age变量，使其随年份变化</span></span>
<span id="cb17-8"><a href="#cb17-8" aria-hidden="true" tabindex="-1"></a>  <span class="fu">mutate</span>(<span class="at">Age =</span> Year <span class="sc">-</span> EstablishYear)</span>
<span id="cb17-9"><a href="#cb17-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-10"><a href="#cb17-10" aria-hidden="true" tabindex="-1"></a><span class="co"># 创建新变量 extended_inspection</span></span>
<span id="cb17-11"><a href="#cb17-11" aria-hidden="true" tabindex="-1"></a>dta3 <span class="ot">&lt;-</span> dta3 <span class="sc">%&gt;%</span></span>
<span id="cb17-12"><a href="#cb17-12" aria-hidden="true" tabindex="-1"></a>  <span class="fu">group_by</span>(Symbol) <span class="sc">%&gt;%</span>  <span class="co"># 按照单位分组</span></span>
<span id="cb17-13"><a href="#cb17-13" aria-hidden="true" tabindex="-1"></a>  <span class="fu">mutate</span>(</span>
<span id="cb17-14"><a href="#cb17-14" aria-hidden="true" tabindex="-1"></a>    <span class="co"># 找到每个单位 first_inspection 为1的年份</span></span>
<span id="cb17-15"><a href="#cb17-15" aria-hidden="true" tabindex="-1"></a>    <span class="at">inspection_year =</span> <span class="fu">ifelse</span>(first_inspection <span class="sc">==</span> <span class="dv">1</span>, Year, <span class="cn">NA</span>),</span>
<span id="cb17-16"><a href="#cb17-16" aria-hidden="true" tabindex="-1"></a>    <span class="co"># 使用 coalesce 处理 NA 值，确保每个单位都有一个 inspection_year</span></span>
<span id="cb17-17"><a href="#cb17-17" aria-hidden="true" tabindex="-1"></a>    <span class="at">inspection_year =</span> <span class="fu">coalesce</span>(inspection_year, <span class="fu">lag</span>(inspection_year, <span class="at">default =</span> <span class="cn">NA</span>))</span>
<span id="cb17-18"><a href="#cb17-18" aria-hidden="true" tabindex="-1"></a>  ) <span class="sc">%&gt;%</span></span>
<span id="cb17-19"><a href="#cb17-19" aria-hidden="true" tabindex="-1"></a>  <span class="co"># 使用 fill 函数确保 inspection_year 被赋值到所有行</span></span>
<span id="cb17-20"><a href="#cb17-20" aria-hidden="true" tabindex="-1"></a>  <span class="fu">fill</span>(inspection_year, <span class="at">.direction =</span> <span class="st">"downup"</span>) <span class="sc">%&gt;%</span></span>
<span id="cb17-21"><a href="#cb17-21" aria-hidden="true" tabindex="-1"></a>  <span class="fu">ungroup</span>()  <span class="co"># 解除分组</span></span>
<span id="cb17-22"><a href="#cb17-22" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-23"><a href="#cb17-23" aria-hidden="true" tabindex="-1"></a><span class="co"># 标记 inspection_year 及之后的两年</span></span>
<span id="cb17-24"><a href="#cb17-24" aria-hidden="true" tabindex="-1"></a>dta3<span class="sc">$</span>extended_inspection <span class="ot">&lt;-</span> <span class="fu">as.numeric</span>(dta3<span class="sc">$</span>Year <span class="sc">&gt;</span> dta3<span class="sc">$</span>inspection_year <span class="sc">&amp;</span> dta3<span class="sc">$</span>Year <span class="sc">&lt;=</span> (dta3<span class="sc">$</span>inspection_year <span class="sc">+</span> <span class="dv">3</span>))</span>
<span id="cb17-25"><a href="#cb17-25" aria-hidden="true" tabindex="-1"></a>dta3 <span class="ot">&lt;-</span> dta3 <span class="sc">%&gt;%</span> <span class="fu">mutate</span>(<span class="at">extended_inspection =</span> <span class="fu">ifelse</span>(<span class="fu">is.na</span>(extended_inspection), <span class="dv">0</span>, extended_inspection))</span>
<span id="cb17-26"><a href="#cb17-26" aria-hidden="true" tabindex="-1"></a><span class="co">#P3: The effects of political connection (continous) and policy pressure on environmental information disclosure</span></span>
<span id="cb17-27"><a href="#cb17-27" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-28"><a href="#cb17-28" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(lme4)</span>
<span id="cb17-29"><a href="#cb17-29" aria-hidden="true" tabindex="-1"></a>p3ext1 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> connection_num <span class="sc">+</span> ROA <span class="sc">+</span> ESG_Rate <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta3)</span>
<span id="cb17-30"><a href="#cb17-30" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-31"><a href="#cb17-31" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-32"><a href="#cb17-32" aria-hidden="true" tabindex="-1"></a>p3ext2 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> extended_inspection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta3)</span>
<span id="cb17-33"><a href="#cb17-33" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-34"><a href="#cb17-34" aria-hidden="true" tabindex="-1"></a>p3ext3 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> extended_inspection <span class="sc">*</span> connection_num <span class="sc">+</span> ESG_Rate <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta3)</span>
<span id="cb17-35"><a href="#cb17-35" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-36"><a href="#cb17-36" aria-hidden="true" tabindex="-1"></a><span class="co"># Create Organization &amp; Environment style table with detailed mixed model statistics</span></span>
<span id="cb17-37"><a href="#cb17-37" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(sjPlot)</span>
<span id="cb17-38"><a href="#cb17-38" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-39"><a href="#cb17-39" aria-hidden="true" tabindex="-1"></a><span class="co"># Define custom CSS for Organization &amp; Environment journal style</span></span>
<span id="cb17-40"><a href="#cb17-40" aria-hidden="true" tabindex="-1"></a>oe_css <span class="ot">&lt;-</span> <span class="fu">list</span>(</span>
<span id="cb17-41"><a href="#cb17-41" aria-hidden="true" tabindex="-1"></a>  <span class="at">css.table =</span> <span class="st">"border-collapse: collapse; border: none; font-family: Times, serif; font-size: 12px;"</span>,</span>
<span id="cb17-42"><a href="#cb17-42" aria-hidden="true" tabindex="-1"></a>  <span class="at">css.thead =</span> <span class="st">"border-top: 2px solid black; border-bottom: 1px solid black; font-weight: normal;"</span>,</span>
<span id="cb17-43"><a href="#cb17-43" aria-hidden="true" tabindex="-1"></a>  <span class="at">css.tdata =</span> <span class="st">"border: none; padding: 2px 8px; text-align: center;"</span>,</span>
<span id="cb17-44"><a href="#cb17-44" aria-hidden="true" tabindex="-1"></a>  <span class="at">css.arc =</span> <span class="st">"border-top: 1px solid black;"</span>,</span>
<span id="cb17-45"><a href="#cb17-45" aria-hidden="true" tabindex="-1"></a>  <span class="at">css.caption =</span> <span class="st">"font-weight: bold; text-align: left; padding-bottom: 10px;"</span>,</span>
<span id="cb17-46"><a href="#cb17-46" aria-hidden="true" tabindex="-1"></a>  <span class="at">css.subtitle =</span> <span class="st">"font-style: italic; text-align: center; padding-bottom: 5px;"</span>,</span>
<span id="cb17-47"><a href="#cb17-47" aria-hidden="true" tabindex="-1"></a>  <span class="at">css.firsttablecol =</span> <span class="st">"text-align: left; padding-left: 0px; border: none;"</span>,</span>
<span id="cb17-48"><a href="#cb17-48" aria-hidden="true" tabindex="-1"></a>  <span class="at">css.leftalign =</span> <span class="st">"text-align: left;"</span>,</span>
<span id="cb17-49"><a href="#cb17-49" aria-hidden="true" tabindex="-1"></a>  <span class="at">css.centeralign =</span> <span class="st">"text-align: center;"</span>,</span>
<span id="cb17-50"><a href="#cb17-50" aria-hidden="true" tabindex="-1"></a>  <span class="at">css.footnote =</span> <span class="st">"font-size: 10px; font-style: italic; border-top: 1px solid black; padding-top: 5px;"</span></span>
<span id="cb17-51"><a href="#cb17-51" aria-hidden="true" tabindex="-1"></a>)</span>
<span id="cb17-52"><a href="#cb17-52" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-53"><a href="#cb17-53" aria-hidden="true" tabindex="-1"></a><span class="fu">tab_model</span>(p3ext1, p3ext2, p3ext3,</span>
<span id="cb17-54"><a href="#cb17-54" aria-hidden="true" tabindex="-1"></a>          <span class="at">title =</span> <span class="st">"Table B2. Short-term Impact of Inspection"</span>,</span>
<span id="cb17-55"><a href="#cb17-55" aria-hidden="true" tabindex="-1"></a>          <span class="at">dv.labels =</span> <span class="st">"Dependent variable:&lt;br&gt;Environmental Information Disclosure"</span>,</span>
<span id="cb17-56"><a href="#cb17-56" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.aic =</span> <span class="cn">TRUE</span>,</span>
<span id="cb17-57"><a href="#cb17-57" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.loglik =</span> <span class="cn">TRUE</span>,</span>
<span id="cb17-58"><a href="#cb17-58" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.obs =</span> <span class="cn">TRUE</span>,</span>
<span id="cb17-59"><a href="#cb17-59" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.ngroups =</span> <span class="cn">TRUE</span>,</span>
<span id="cb17-60"><a href="#cb17-60" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.re.var =</span> <span class="cn">TRUE</span>,</span>
<span id="cb17-61"><a href="#cb17-61" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.icc =</span> <span class="cn">TRUE</span>,</span>
<span id="cb17-62"><a href="#cb17-62" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.intercept =</span> <span class="cn">FALSE</span>, <span class="co"># Hide intercept</span></span>
<span id="cb17-63"><a href="#cb17-63" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.p =</span> <span class="cn">FALSE</span>,        <span class="co"># Hide p-values column</span></span>
<span id="cb17-64"><a href="#cb17-64" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.se =</span> <span class="cn">TRUE</span>,        <span class="co"># Show standard errors in parentheses</span></span>
<span id="cb17-65"><a href="#cb17-65" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.ci =</span> <span class="cn">FALSE</span>,       <span class="co"># Hide confidence intervals</span></span>
<span id="cb17-66"><a href="#cb17-66" aria-hidden="true" tabindex="-1"></a>          <span class="at">p.style =</span> <span class="st">"stars"</span>,     <span class="co"># Display significance as stars</span></span>
<span id="cb17-67"><a href="#cb17-67" aria-hidden="true" tabindex="-1"></a>          <span class="at">p.threshold =</span> <span class="fu">c</span>(<span class="fl">0.1</span>, <span class="fl">0.05</span>, <span class="fl">0.01</span>),  <span class="co"># Set significance thresholds</span></span>
<span id="cb17-68"><a href="#cb17-68" aria-hidden="true" tabindex="-1"></a>          <span class="at">digits =</span> <span class="dv">3</span>,            <span class="co"># 3 decimal places</span></span>
<span id="cb17-69"><a href="#cb17-69" aria-hidden="true" tabindex="-1"></a>          <span class="at">digits.re =</span> <span class="dv">2</span>,         <span class="co"># 2 decimal places for random effects</span></span>
<span id="cb17-70"><a href="#cb17-70" aria-hidden="true" tabindex="-1"></a>          <span class="at">pred.labels =</span> <span class="fu">c</span>(<span class="st">"Age"</span>, <span class="st">"connection"</span>, <span class="st">"inspection influence"</span>, <span class="st">"ROA"</span>, <span class="st">"env_rate"</span>,</span>
<span id="cb17-71"><a href="#cb17-71" aria-hidden="true" tabindex="-1"></a>                         <span class="st">"leverage"</span>, <span class="st">"inspection influence: connection"</span>, <span class="st">"register capital (log)"</span>),</span>
<span id="cb17-72"><a href="#cb17-72" aria-hidden="true" tabindex="-1"></a>          <span class="at">CSS =</span> oe_css,</span>
<span id="cb17-73"><a href="#cb17-73" aria-hidden="true" tabindex="-1"></a>          <span class="at">file =</span> <span class="st">"B2_p3_extended_inspection_OE_style.html"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output-display">
<table style="border-collapse: collapse; border: none; font-family: Times, serif; font-size: 12px;">
<caption style="font-weight: bold; text-align: left; padding-bottom: 10px;">Table B2. Short-term Impact of Inspection</caption>
<tbody><tr>
<th style="border-top: 2px solid black; border-bottom: 1px solid black; font-weight: normal; border-bottom:1px solid black; text-align: left; padding-left: 0px; border: none; ">&nbsp;</th>
<th colspan="2" style="border-top: 2px solid black; border-bottom: 1px solid black; font-weight: normal; border-bottom:1px solid black;">Dependent variable:<br>Environmental Information Disclosure</th>
</tr>
<tr>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; text-align: left; padding-left: 0px; border: none; ">Predictors</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">std. Error</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">std. Error</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; col7">std. Error</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">Age</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.253 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.020</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.243 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.020</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.236 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.020</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">connection</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">-0.286 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.046</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">-0.372 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.052</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">inspection influence</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.018 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.104</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.020 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.104</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.019 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.103</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">ROA</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.266 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.014</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.264 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.014</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.265 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.014</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">env_rate</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.001 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.002</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.001 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.002</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.001 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.002</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">leverage</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">3.008 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.112</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">2.744 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.106</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">2.980 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.112</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">inspection influence: connection</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">1.089 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.248</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.204 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.314</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">register capital (log)</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.409 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.099</td>
</tr>
<tr>
<td colspan="7" style="font-weight:bold; text-align:left; padding-top:.8em;">Random Effects</td>
</tr>

<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">σ<sup>2</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">126.34</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">126.59</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">125.98</td>
</tr>

<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">τ<sub>00</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">12.70 <sub>CITY:PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">12.68 <sub>CITY:PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">12.63 <sub>CITY:PROVINCE</sub></td>

</tr><tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">1.45 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">1.22 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">1.42 <sub>PROVINCE</sub></td>

</tr><tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">ICC</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.10</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.10</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.10</td>

</tr><tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">N</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>

</tr><tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
</tr><tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm; border-top:1px solid;">Observations</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10771</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10771</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10771</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">Marginal R<sup>2</sup> / Conditional R<sup>2</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.129 / 0.217</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.127 / 0.213</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.131 / 0.218</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">AIC</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">83024.306</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">83040.019</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">82998.625</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">log-Likelihood</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-41502.153</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-41510.009</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-41487.312</td>
</tr>
<tr>
<td colspan="7" style="font-size: 10px; font-style: italic; border-top: 1px solid black; padding-top: 5px;">* p&lt;0.1&nbsp;&nbsp;&nbsp;** p&lt;0.05&nbsp;&nbsp;&nbsp;*** p&lt;0.01</td>
</tr>

</tbody></table>

</div>
</div>
</section>
<section id="p4-contrallocal-connection-continuous" class="level3">
<h3 class="anchored" data-anchor-id="p4-contrallocal-connection-continuous">P4 contral/local connection (continuous)</h3>
<p>Table B3. Short-term Impact of Inspection (central and local connections)</p>
<div class="cell">
<div class="sourceCode cell-code" id="cb18"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb18-1"><a href="#cb18-1" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(lme4)</span>
<span id="cb18-2"><a href="#cb18-2" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb18-3"><a href="#cb18-3" aria-hidden="true" tabindex="-1"></a>p4ext1 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> central_connection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta3)</span>
<span id="cb18-4"><a href="#cb18-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb18-5"><a href="#cb18-5" aria-hidden="true" tabindex="-1"></a>p4ext2 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> central_connection <span class="sc">*</span> extended_inspection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta3)</span>
<span id="cb18-6"><a href="#cb18-6" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb18-7"><a href="#cb18-7" aria-hidden="true" tabindex="-1"></a>p4ext3 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> local_connection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta3)</span>
<span id="cb18-8"><a href="#cb18-8" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb18-9"><a href="#cb18-9" aria-hidden="true" tabindex="-1"></a>p4ext4 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> local_connection <span class="sc">*</span> extended_inspection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta3)</span>
<span id="cb18-10"><a href="#cb18-10" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb18-11"><a href="#cb18-11" aria-hidden="true" tabindex="-1"></a><span class="co"># Create Organization &amp; Environment style table for central/local connections</span></span>
<span id="cb18-12"><a href="#cb18-12" aria-hidden="true" tabindex="-1"></a><span class="fu">tab_model</span>(p4ext1, p4ext2, p4ext3, p4ext4,</span>
<span id="cb18-13"><a href="#cb18-13" aria-hidden="true" tabindex="-1"></a>          <span class="at">title =</span> <span class="st">"Table B3. Short-term Impact of Inspection (Central and Local Connections)"</span>,</span>
<span id="cb18-14"><a href="#cb18-14" aria-hidden="true" tabindex="-1"></a>          <span class="at">dv.labels =</span> <span class="st">"Dependent variable:&lt;br&gt;Environmental Information Disclosure"</span>,</span>
<span id="cb18-15"><a href="#cb18-15" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.aic =</span> <span class="cn">TRUE</span>,</span>
<span id="cb18-16"><a href="#cb18-16" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.loglik =</span> <span class="cn">TRUE</span>,</span>
<span id="cb18-17"><a href="#cb18-17" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.obs =</span> <span class="cn">TRUE</span>,</span>
<span id="cb18-18"><a href="#cb18-18" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.ngroups =</span> <span class="cn">TRUE</span>,</span>
<span id="cb18-19"><a href="#cb18-19" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.re.var =</span> <span class="cn">TRUE</span>,</span>
<span id="cb18-20"><a href="#cb18-20" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.icc =</span> <span class="cn">TRUE</span>,</span>
<span id="cb18-21"><a href="#cb18-21" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.intercept =</span> <span class="cn">FALSE</span>, <span class="co"># Hide intercept</span></span>
<span id="cb18-22"><a href="#cb18-22" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.p =</span> <span class="cn">FALSE</span>,        <span class="co"># Hide p-values column</span></span>
<span id="cb18-23"><a href="#cb18-23" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.se =</span> <span class="cn">TRUE</span>,        <span class="co"># Show standard errors in parentheses</span></span>
<span id="cb18-24"><a href="#cb18-24" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.ci =</span> <span class="cn">FALSE</span>,       <span class="co"># Hide confidence intervals</span></span>
<span id="cb18-25"><a href="#cb18-25" aria-hidden="true" tabindex="-1"></a>          <span class="at">p.style =</span> <span class="st">"stars"</span>,     <span class="co"># Display significance as stars</span></span>
<span id="cb18-26"><a href="#cb18-26" aria-hidden="true" tabindex="-1"></a>          <span class="at">p.threshold =</span> <span class="fu">c</span>(<span class="fl">0.1</span>, <span class="fl">0.05</span>, <span class="fl">0.01</span>),  <span class="co"># Set significance thresholds</span></span>
<span id="cb18-27"><a href="#cb18-27" aria-hidden="true" tabindex="-1"></a>          <span class="at">digits =</span> <span class="dv">3</span>,            <span class="co"># 3 decimal places</span></span>
<span id="cb18-28"><a href="#cb18-28" aria-hidden="true" tabindex="-1"></a>          <span class="at">digits.re =</span> <span class="dv">2</span>,         <span class="co"># 2 decimal places for random effects</span></span>
<span id="cb18-29"><a href="#cb18-29" aria-hidden="true" tabindex="-1"></a>          <span class="at">CSS =</span> oe_css,</span>
<span id="cb18-30"><a href="#cb18-30" aria-hidden="true" tabindex="-1"></a>          <span class="at">file =</span> <span class="st">"B3_p4_extended_inspection_OE_style.html"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output-display">
<table style="border-collapse: collapse; border: none; font-family: Times, serif; font-size: 12px;">
<caption style="font-weight: bold; text-align: left; padding-bottom: 10px;">Table B3. Short-term Impact of Inspection (Central and Local Connections)</caption>
<tbody><tr>
<th style="border-top: 2px solid black; border-bottom: 1px solid black; font-weight: normal; border-bottom:1px solid black; text-align: left; padding-left: 0px; border: none; ">&nbsp;</th>
<th colspan="2" style="border-top: 2px solid black; border-bottom: 1px solid black; font-weight: normal; border-bottom:1px solid black;">Dependent variable:<br>Environmental Information Disclosure</th>
</tr>
<tr>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; text-align: left; padding-left: 0px; border: none; ">Predictors</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">std. Error</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">std. Error</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; col7">std. Error</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; col8">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; col9">std. Error</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">Age</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.253 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.020</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.237 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.020</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.256 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.020</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col8">0.238 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col9">0.020</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">central connection</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">-0.755 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.176</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">-0.946 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.193</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7"></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col8"></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col9"></td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">ESG Rate</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.268 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.014</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.266 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.014</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.265 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.014</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col8">0.264 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col9">0.014</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">RegisterCapital log</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">2.914 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.110</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">2.877 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.111</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">2.966 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.110</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col8">2.941 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col9">0.111</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">ROA</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.019 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.104</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.020 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.104</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.018 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.104</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col8">0.020 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col9">0.103</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">Leverage</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.001 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.002</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.001 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.002</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.001 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.002</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col8">0.001 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col9">0.002</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">extended inspection</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.753 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.263</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7"></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col8">0.278 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col9">0.313</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">central connection ×<br>extended inspection</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">1.265 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.414</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7"></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col8"></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col9"></td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">local connection</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">-0.299 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.052</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col8">-0.400 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col9">0.059</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">local connection ×<br>extended inspection</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7"></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col8">0.430 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col9">0.110</td>
</tr>
<tr>
<td colspan="9" style="font-weight:bold; text-align:left; padding-top:.8em;">Random Effects</td>
</tr>

<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">σ<sup>2</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">126.57</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">126.30</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">126.41</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">126.06</td>
</tr>

<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">τ<sub>00</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">12.92 <sub>CITY:PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">12.79 <sub>CITY:PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">12.66 <sub>CITY:PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">12.58 <sub>CITY:PROVINCE</sub></td>

</tr><tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">1.23 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">1.26 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">1.46 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">1.42 <sub>PROVINCE</sub></td>

</tr><tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">ICC</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.10</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.10</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.10</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.10</td>

</tr><tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">N</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>

</tr><tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
</tr><tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm; border-top:1px solid;">Observations</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10771</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10771</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10771</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10771</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">Marginal R<sup>2</sup> / Conditional R<sup>2</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.127 / 0.215</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.129 / 0.216</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.129 / 0.216</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.131 / 0.218</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">AIC</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">83041.522</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">83020.298</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">83029.624</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">83004.713</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">log-Likelihood</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-41510.761</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-41498.149</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-41504.812</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-41490.356</td>
</tr>
<tr>
<td colspan="9" style="font-size: 10px; font-style: italic; border-top: 1px solid black; padding-top: 5px;">* p&lt;0.1&nbsp;&nbsp;&nbsp;** p&lt;0.05&nbsp;&nbsp;&nbsp;*** p&lt;0.01</td>
</tr>

</tbody></table>

</div>
</div>
</section>
</section>
<section id="state-owned-enterprises" class="level2">
<h2 class="anchored" data-anchor-id="state-owned-enterprises">State-owned Enterprises</h2>
<section id="p3-soe-new-dataset" class="level3">
<h3 class="anchored" data-anchor-id="p3-soe-new-dataset">P3 SOE new dataset</h3>
<p>Table B4. State-owned Enterprises We labelled firms where the actual controllers are state-owned institutions or state-owned enterprises as politically connected.</p>
<div class="cell">
<div class="sourceCode cell-code" id="cb19"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb19-1"><a href="#cb19-1" aria-hidden="true" tabindex="-1"></a><span class="co">#P3</span></span>
<span id="cb19-2"><a href="#cb19-2" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-3"><a href="#cb19-3" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(lme4)</span>
<span id="cb19-4"><a href="#cb19-4" aria-hidden="true" tabindex="-1"></a>p1soe1 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> SOE_new <span class="sc">+</span> ROA <span class="sc">+</span> ESG_Rate <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta1)</span>
<span id="cb19-5"><a href="#cb19-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-6"><a href="#cb19-6" aria-hidden="true" tabindex="-1"></a>p1soe2 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> after_first_inspection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta1)</span>
<span id="cb19-7"><a href="#cb19-7" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-8"><a href="#cb19-8" aria-hidden="true" tabindex="-1"></a>p1soe3 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> after_first_inspection <span class="sc">*</span> SOE_new <span class="sc">+</span> ESG_Rate <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta1)</span>
<span id="cb19-9"><a href="#cb19-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-10"><a href="#cb19-10" aria-hidden="true" tabindex="-1"></a><span class="co"># Create Organization &amp; Environment style table for SOE analysis</span></span>
<span id="cb19-11"><a href="#cb19-11" aria-hidden="true" tabindex="-1"></a><span class="fu">tab_model</span>(p1soe1, p1soe2, p1soe3,</span>
<span id="cb19-12"><a href="#cb19-12" aria-hidden="true" tabindex="-1"></a>          <span class="at">title =</span> <span class="st">"Table B4. State-owned Enterprises and Environmental Disclosure"</span>,</span>
<span id="cb19-13"><a href="#cb19-13" aria-hidden="true" tabindex="-1"></a>          <span class="at">dv.labels =</span> <span class="st">"Dependent variable:&lt;br&gt;Environmental Information Disclosure"</span>,</span>
<span id="cb19-14"><a href="#cb19-14" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.aic =</span> <span class="cn">TRUE</span>,</span>
<span id="cb19-15"><a href="#cb19-15" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.loglik =</span> <span class="cn">TRUE</span>,</span>
<span id="cb19-16"><a href="#cb19-16" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.obs =</span> <span class="cn">TRUE</span>,</span>
<span id="cb19-17"><a href="#cb19-17" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.ngroups =</span> <span class="cn">TRUE</span>,</span>
<span id="cb19-18"><a href="#cb19-18" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.re.var =</span> <span class="cn">TRUE</span>,</span>
<span id="cb19-19"><a href="#cb19-19" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.icc =</span> <span class="cn">TRUE</span>,</span>
<span id="cb19-20"><a href="#cb19-20" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.intercept =</span> <span class="cn">FALSE</span>, <span class="co"># Hide intercept</span></span>
<span id="cb19-21"><a href="#cb19-21" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.p =</span> <span class="cn">FALSE</span>,        <span class="co"># Hide p-values column</span></span>
<span id="cb19-22"><a href="#cb19-22" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.se =</span> <span class="cn">TRUE</span>,        <span class="co"># Show standard errors in parentheses</span></span>
<span id="cb19-23"><a href="#cb19-23" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.ci =</span> <span class="cn">FALSE</span>,       <span class="co"># Hide confidence intervals</span></span>
<span id="cb19-24"><a href="#cb19-24" aria-hidden="true" tabindex="-1"></a>          <span class="at">p.style =</span> <span class="st">"stars"</span>,     <span class="co"># Display significance as stars</span></span>
<span id="cb19-25"><a href="#cb19-25" aria-hidden="true" tabindex="-1"></a>          <span class="at">p.threshold =</span> <span class="fu">c</span>(<span class="fl">0.1</span>, <span class="fl">0.05</span>, <span class="fl">0.01</span>),  <span class="co"># Set significance thresholds</span></span>
<span id="cb19-26"><a href="#cb19-26" aria-hidden="true" tabindex="-1"></a>          <span class="at">digits =</span> <span class="dv">3</span>,            <span class="co"># 3 decimal places</span></span>
<span id="cb19-27"><a href="#cb19-27" aria-hidden="true" tabindex="-1"></a>          <span class="at">digits.re =</span> <span class="dv">2</span>,         <span class="co"># 2 decimal places for random effects</span></span>
<span id="cb19-28"><a href="#cb19-28" aria-hidden="true" tabindex="-1"></a>          <span class="at">CSS =</span> oe_css,</span>
<span id="cb19-29"><a href="#cb19-29" aria-hidden="true" tabindex="-1"></a>          <span class="at">file =</span> <span class="st">"B4_SOE_result_p1_OE_style.html"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output-display">
<table style="border-collapse: collapse; border: none; font-family: Times, serif; font-size: 12px;">
<caption style="font-weight: bold; text-align: left; padding-bottom: 10px;">Table B4. State-owned Enterprises and Environmental Disclosure</caption>
<tbody><tr>
<th style="border-top: 2px solid black; border-bottom: 1px solid black; font-weight: normal; border-bottom:1px solid black; text-align: left; padding-left: 0px; border: none; ">&nbsp;</th>
<th colspan="2" style="border-top: 2px solid black; border-bottom: 1px solid black; font-weight: normal; border-bottom:1px solid black;">Dependent variable:<br>Environmental Information Disclosure</th>
</tr>
<tr>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; text-align: left; padding-left: 0px; border: none; ">Predictors</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">std. Error</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">std. Error</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; col7">std. Error</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">Age</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.273 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.020</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.045 <sup>**</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.021</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">SOE new</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">-1.261 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.249</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">-1.008 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.345</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">ROA</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.011 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.104</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.037 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.100</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.031 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.100</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">ESG Rate</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.270 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.014</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.261 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.014</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.261 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.014</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">Leverage</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.001 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.002</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.001 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.002</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.001 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.002</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">RegisterCapital log</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">2.866 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.107</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">2.501 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.103</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">2.489 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.105</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">after first inspection</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">6.609 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.218</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">5.715 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.325</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">after first inspection ×<br>SOE new</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">1.265 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.430</td>
</tr>
<tr>
<td colspan="7" style="font-weight:bold; text-align:left; padding-top:.8em;">Random Effects</td>
</tr>

<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">σ<sup>2</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">126.49</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">118.99</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">118.67</td>
</tr>

<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">τ<sub>00</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">12.82 <sub>CITY:PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">11.28 <sub>CITY:PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">11.33 <sub>CITY:PROVINCE</sub></td>

</tr><tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">1.30 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">1.51 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">1.48 <sub>PROVINCE</sub></td>

</tr><tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">ICC</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.10</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.10</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.10</td>

</tr><tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">N</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>

</tr><tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
</tr><tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm; border-top:1px solid;">Observations</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10771</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10777</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10771</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">Marginal R<sup>2</sup> / Conditional R<sup>2</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.127 / 0.215</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.179 / 0.258</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.178 / 0.258</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">AIC</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">83033.670</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">82407.560</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">82343.522</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">log-Likelihood</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-41506.835</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-41194.780</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-41159.761</td>
</tr>
<tr>
<td colspan="7" style="font-size: 10px; font-style: italic; border-top: 1px solid black; padding-top: 5px;">* p&lt;0.1&nbsp;&nbsp;&nbsp;** p&lt;0.05&nbsp;&nbsp;&nbsp;*** p&lt;0.01</td>
</tr>

</tbody></table>

</div>
</div>
</section>
<section id="p4-centrallocal-soe-level" class="level3">
<h3 class="anchored" data-anchor-id="p4-centrallocal-soe-level">P4 central/local SOE Level</h3>
<div class="cell">
<div class="sourceCode cell-code" id="cb20"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb20-1"><a href="#cb20-1" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(lme4)</span>
<span id="cb20-2"><a href="#cb20-2" aria-hidden="true" tabindex="-1"></a>p2soe1 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> SOE_new_central <span class="sc">+</span> Age <span class="sc">+</span> ESG_Rate <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta1)</span>
<span id="cb20-3"><a href="#cb20-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb20-4"><a href="#cb20-4" aria-hidden="true" tabindex="-1"></a>p2soe2 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> SOE_new_central <span class="sc">*</span> after_first_inspection <span class="sc">+</span> Age <span class="sc">+</span> ESG_Rate <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta1)</span>
<span id="cb20-5"><a href="#cb20-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb20-6"><a href="#cb20-6" aria-hidden="true" tabindex="-1"></a>p2soe3 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> SOE_new_local <span class="sc">+</span> Age <span class="sc">+</span> ESG_Rate <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta1)</span>
<span id="cb20-7"><a href="#cb20-7" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb20-8"><a href="#cb20-8" aria-hidden="true" tabindex="-1"></a>p2soe4 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> SOE_new_local <span class="sc">*</span> after_first_inspection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> (<span class="dv">1</span><span class="sc">|</span>PROVINCE<span class="sc">/</span>CITY), <span class="at">data=</span>dta1)</span>
<span id="cb20-9"><a href="#cb20-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb20-10"><a href="#cb20-10" aria-hidden="true" tabindex="-1"></a><span class="co"># Create Organization &amp; Environment style table for central/local SOE analysis</span></span>
<span id="cb20-11"><a href="#cb20-11" aria-hidden="true" tabindex="-1"></a><span class="fu">tab_model</span>(p2soe1, p2soe2, p2soe3, p2soe4,</span>
<span id="cb20-12"><a href="#cb20-12" aria-hidden="true" tabindex="-1"></a>          <span class="at">title =</span> <span class="st">"Table B5. Central vs Local State-owned Enterprises"</span>,</span>
<span id="cb20-13"><a href="#cb20-13" aria-hidden="true" tabindex="-1"></a>          <span class="at">dv.labels =</span> <span class="st">"Dependent variable:&lt;br&gt;Environmental Information Disclosure"</span>,</span>
<span id="cb20-14"><a href="#cb20-14" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.aic =</span> <span class="cn">TRUE</span>,</span>
<span id="cb20-15"><a href="#cb20-15" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.loglik =</span> <span class="cn">TRUE</span>,</span>
<span id="cb20-16"><a href="#cb20-16" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.obs =</span> <span class="cn">TRUE</span>,</span>
<span id="cb20-17"><a href="#cb20-17" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.ngroups =</span> <span class="cn">TRUE</span>,</span>
<span id="cb20-18"><a href="#cb20-18" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.re.var =</span> <span class="cn">TRUE</span>,</span>
<span id="cb20-19"><a href="#cb20-19" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.icc =</span> <span class="cn">TRUE</span>,</span>
<span id="cb20-20"><a href="#cb20-20" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.intercept =</span> <span class="cn">FALSE</span>, <span class="co"># Hide intercept</span></span>
<span id="cb20-21"><a href="#cb20-21" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.p =</span> <span class="cn">FALSE</span>,        <span class="co"># Hide p-values column</span></span>
<span id="cb20-22"><a href="#cb20-22" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.se =</span> <span class="cn">TRUE</span>,        <span class="co"># Show standard errors in parentheses</span></span>
<span id="cb20-23"><a href="#cb20-23" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.ci =</span> <span class="cn">FALSE</span>,       <span class="co"># Hide confidence intervals</span></span>
<span id="cb20-24"><a href="#cb20-24" aria-hidden="true" tabindex="-1"></a>          <span class="at">p.style =</span> <span class="st">"stars"</span>,     <span class="co"># Display significance as stars</span></span>
<span id="cb20-25"><a href="#cb20-25" aria-hidden="true" tabindex="-1"></a>          <span class="at">p.threshold =</span> <span class="fu">c</span>(<span class="fl">0.1</span>, <span class="fl">0.05</span>, <span class="fl">0.01</span>),  <span class="co"># Set significance thresholds</span></span>
<span id="cb20-26"><a href="#cb20-26" aria-hidden="true" tabindex="-1"></a>          <span class="at">digits =</span> <span class="dv">3</span>,            <span class="co"># 3 decimal places</span></span>
<span id="cb20-27"><a href="#cb20-27" aria-hidden="true" tabindex="-1"></a>          <span class="at">digits.re =</span> <span class="dv">2</span>,         <span class="co"># 2 decimal places for random effects</span></span>
<span id="cb20-28"><a href="#cb20-28" aria-hidden="true" tabindex="-1"></a>          <span class="at">CSS =</span> oe_css,</span>
<span id="cb20-29"><a href="#cb20-29" aria-hidden="true" tabindex="-1"></a>          <span class="at">file =</span> <span class="st">"B5_SOE_result_p2_OE_style.html"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output-display">
<table style="border-collapse: collapse; border: none; font-family: Times, serif; font-size: 12px;">
<caption style="font-weight: bold; text-align: left; padding-bottom: 10px;">Table B5. Central vs Local State-owned Enterprises</caption>
<tbody><tr>
<th style="border-top: 2px solid black; border-bottom: 1px solid black; font-weight: normal; border-bottom:1px solid black; text-align: left; padding-left: 0px; border: none; ">&nbsp;</th>
<th colspan="2" style="border-top: 2px solid black; border-bottom: 1px solid black; font-weight: normal; border-bottom:1px solid black;">Dependent variable:<br>Environmental Information Disclosure</th>
</tr>
<tr>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; text-align: left; padding-left: 0px; border: none; ">Predictors</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">std. Error</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">std. Error</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; col7">std. Error</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; col8">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal; border-bottom:1px solid black; col9">std. Error</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">SOE new central</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">1.452 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.306</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.944 <sup>**</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.423</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7"></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col8"></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col9"></td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">Age</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.263 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.020</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.042 <sup>**</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.021</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.287 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.020</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col8">0.066 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col9">0.021</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">ESG Rate</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.259 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.014</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.252 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.014</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.264 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.014</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col8">0.260 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col9">0.014</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">RegisterCapital log</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">2.718 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.107</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">2.378 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.104</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">2.829 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.106</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col8">2.493 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col9">0.103</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">ROA</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.022 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.104</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.039 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.100</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.011 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.103</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col8">0.030 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col9">0.100</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">Leverage</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.001 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.002</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.001 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.002</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.001 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.002</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col8">0.001 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col9">0.002</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">after first inspection</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">6.171 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.259</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7"></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col8">6.142 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col9">0.277</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">SOE new central × after<br>first inspection</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">1.404 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">0.531</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7"></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col8"></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col9"></td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">SOE new local</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  ">-2.434 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7">0.260</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col8">-1.722 <sup>***</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col9">0.354</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-left: 0px; border: none; ">SOE new local × after<br>first inspection</td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  "></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col7"></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col8">0.242 <sup></sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: center;  col9">0.461</td>
</tr>
<tr>
<td colspan="9" style="font-weight:bold; text-align:left; padding-top:.8em;">Random Effects</td>
</tr>

<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">σ<sup>2</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">126.51</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">118.28</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">125.70</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">118.32</td>
</tr>

<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">τ<sub>00</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">12.98 <sub>CITY:PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">11.61 <sub>CITY:PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">13.12 <sub>CITY:PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">11.39 <sub>CITY:PROVINCE</sub></td>

</tr><tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">1.32 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">1.57 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">1.55 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">1.71 <sub>PROVINCE</sub></td>

</tr><tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">ICC</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.10</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.10</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.10</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.10</td>

</tr><tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">N</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">252 <sub>CITY</sub></td>

</tr><tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">32 <sub>PROVINCE</sub></td>
</tr><tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm; border-top:1px solid;">Observations</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10771</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10771</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10771</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center; border-top:1px solid;" colspan="2">10771</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">Marginal R<sup>2</sup> / Conditional R<sup>2</sup></td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.128 / 0.217</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.181 / 0.264</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.133 / 0.224</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">0.181 / 0.262</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">AIC</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">83036.410</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">82311.324</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">82971.874</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">82313.827</td>
</tr>
<tr>
<td style=" border: none; padding: 2px 8px; text-align: center; text-align: left; padding-top:0.1cm; padding-bottom:0.1cm;">log-Likelihood</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-41508.205</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-41143.662</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-41475.937</td>
<td style=" border: none; padding: 2px 8px; text-align: center; padding-top:0.1cm; padding-bottom:0.1cm; text-align:center;" colspan="2">-41144.914</td>
</tr>
<tr>
<td colspan="9" style="font-size: 10px; font-style: italic; border-top: 1px solid black; padding-top: 5px;">* p&lt;0.1&nbsp;&nbsp;&nbsp;** p&lt;0.05&nbsp;&nbsp;&nbsp;*** p&lt;0.01</td>
</tr>

</tbody></table>

</div>
</div>
</section>
</section>
<section id="plm-model" class="level2">
<h2 class="anchored" data-anchor-id="plm-model">Plm model</h2>
<section id="p3-connection-continuous-1" class="level3">
<h3 class="anchored" data-anchor-id="p3-connection-continuous-1">P3 connection (continuous)</h3>
<div class="cell">
<div class="sourceCode cell-code" id="cb21"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb21-1"><a href="#cb21-1" aria-hidden="true" tabindex="-1"></a><span class="co">#P3: The effects of political connection (continous) and policy pressure on environmental information disclosure</span></span>
<span id="cb21-2"><a href="#cb21-2" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(plm)</span>
<span id="cb21-3"><a href="#cb21-3" aria-hidden="true" tabindex="-1"></a>p3plm1 <span class="ot">&lt;-</span> <span class="fu">plm</span>(Environmental_Information_Disclosure <span class="sc">~</span> connection_num <span class="sc">+</span> ROA <span class="sc">+</span> ESG_Rate <span class="sc">+</span> Leverage <span class="sc">+</span> <span class="fu">as.factor</span>(IndustryName)<span class="sc">+</span> <span class="fu">as.factor</span>(PROVINCE) <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ESG_Rate, <span class="at">data=</span>dta1,</span>
<span id="cb21-4"><a href="#cb21-4" aria-hidden="true" tabindex="-1"></a>              <span class="at">index=</span><span class="fu">c</span>(<span class="st">"Symbol"</span>, <span class="st">"EndYear"</span>),<span class="at">model=</span><span class="st">"within"</span>,<span class="at">effect=</span><span class="st">"twoways"</span>)</span>
<span id="cb21-5"><a href="#cb21-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb21-6"><a href="#cb21-6" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb21-7"><a href="#cb21-7" aria-hidden="true" tabindex="-1"></a>p3plm2 <span class="ot">&lt;-</span> <span class="fu">plm</span>(Environmental_Information_Disclosure <span class="sc">~</span> after_first_inspection <span class="sc">+</span> ESG_Rate  <span class="sc">+</span> <span class="fu">as.factor</span>(IndustryName)<span class="sc">+</span> <span class="fu">as.factor</span>(PROVINCE) <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span>ESG_Rate, <span class="at">data=</span>dta1,</span>
<span id="cb21-8"><a href="#cb21-8" aria-hidden="true" tabindex="-1"></a>              <span class="at">index=</span><span class="fu">c</span>(<span class="st">"Symbol"</span>),<span class="at">model=</span><span class="st">"within"</span>)</span>
<span id="cb21-9"><a href="#cb21-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb21-10"><a href="#cb21-10" aria-hidden="true" tabindex="-1"></a>p3plm3 <span class="ot">&lt;-</span> <span class="fu">plm</span>(Environmental_Information_Disclosure <span class="sc">~</span> after_first_inspection <span class="sc">*</span> connection_num <span class="sc">+</span> ESG_Rate  <span class="sc">+</span> <span class="fu">as.factor</span>(IndustryName)<span class="sc">+</span> <span class="fu">as.factor</span>(PROVINCE) <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> ESG_Rate, <span class="at">data=</span>dta1,</span>
<span id="cb21-11"><a href="#cb21-11" aria-hidden="true" tabindex="-1"></a>              <span class="at">index=</span><span class="fu">c</span>(<span class="st">"Symbol"</span>),<span class="at">model=</span><span class="st">"within"</span>)</span>
<span id="cb21-12"><a href="#cb21-12" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb21-13"><a href="#cb21-13" aria-hidden="true" tabindex="-1"></a><span class="co"># Create Organization &amp; Environment style table for PLM models using modelsummary</span></span>
<span id="cb21-14"><a href="#cb21-14" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(modelsummary)</span>
<span id="cb21-15"><a href="#cb21-15" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb21-16"><a href="#cb21-16" aria-hidden="true" tabindex="-1"></a><span class="co"># Define coefficient names for cleaner display</span></span>
<span id="cb21-17"><a href="#cb21-17" aria-hidden="true" tabindex="-1"></a>coef_map_plm <span class="ot">&lt;-</span> <span class="fu">c</span>(</span>
<span id="cb21-18"><a href="#cb21-18" aria-hidden="true" tabindex="-1"></a>  <span class="st">"Age"</span> <span class="ot">=</span> <span class="st">"Age"</span>,</span>
<span id="cb21-19"><a href="#cb21-19" aria-hidden="true" tabindex="-1"></a>  <span class="st">"connection_num"</span> <span class="ot">=</span> <span class="st">"Connections"</span>,</span>
<span id="cb21-20"><a href="#cb21-20" aria-hidden="true" tabindex="-1"></a>  <span class="st">"after_first_inspection"</span> <span class="ot">=</span> <span class="st">"After First Inspection"</span>,</span>
<span id="cb21-21"><a href="#cb21-21" aria-hidden="true" tabindex="-1"></a>  <span class="st">"ESG_Rate"</span> <span class="ot">=</span> <span class="st">"ESG Rating"</span>,</span>
<span id="cb21-22"><a href="#cb21-22" aria-hidden="true" tabindex="-1"></a>  <span class="st">"ROA"</span> <span class="ot">=</span> <span class="st">"Return on Assets"</span>,</span>
<span id="cb21-23"><a href="#cb21-23" aria-hidden="true" tabindex="-1"></a>  <span class="st">"Leverage"</span> <span class="ot">=</span> <span class="st">"Leverage"</span>,</span>
<span id="cb21-24"><a href="#cb21-24" aria-hidden="true" tabindex="-1"></a>  <span class="st">"RegisterCapital_log"</span> <span class="ot">=</span> <span class="st">"Log(Registered Capital)"</span>,</span>
<span id="cb21-25"><a href="#cb21-25" aria-hidden="true" tabindex="-1"></a>  <span class="st">"connection_num:after_first_inspection"</span> <span class="ot">=</span> <span class="st">"Connections × After Inspection"</span></span>
<span id="cb21-26"><a href="#cb21-26" aria-hidden="true" tabindex="-1"></a>)</span>
<span id="cb21-27"><a href="#cb21-27" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb21-28"><a href="#cb21-28" aria-hidden="true" tabindex="-1"></a><span class="co"># Create rows for fixed effects that will appear before statistics</span></span>
<span id="cb21-29"><a href="#cb21-29" aria-hidden="true" tabindex="-1"></a>fe_rows <span class="ot">&lt;-</span> <span class="fu">tribble</span>(</span>
<span id="cb21-30"><a href="#cb21-30" aria-hidden="true" tabindex="-1"></a>  <span class="sc">~</span>term, <span class="sc">~</span><span class="st">`</span><span class="at">Two-way FE</span><span class="st">`</span>, <span class="sc">~</span><span class="st">`</span><span class="at">Individual FE</span><span class="st">`</span>, <span class="sc">~</span><span class="st">`</span><span class="at">Interaction Model</span><span class="st">`</span>,</span>
<span id="cb21-31"><a href="#cb21-31" aria-hidden="true" tabindex="-1"></a>  <span class="st">"Firm fixed effects"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>,</span>
<span id="cb21-32"><a href="#cb21-32" aria-hidden="true" tabindex="-1"></a>  <span class="st">"Industry fixed effects"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>,</span>
<span id="cb21-33"><a href="#cb21-33" aria-hidden="true" tabindex="-1"></a>  <span class="st">"Province fixed effects"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>,</span>
<span id="cb21-34"><a href="#cb21-34" aria-hidden="true" tabindex="-1"></a>  <span class="st">"Year fixed effects"</span>, <span class="st">"Y"</span>, <span class="st">"N"</span>, <span class="st">"N"</span></span>
<span id="cb21-35"><a href="#cb21-35" aria-hidden="true" tabindex="-1"></a>)</span>
<span id="cb21-36"><a href="#cb21-36" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb21-37"><a href="#cb21-37" aria-hidden="true" tabindex="-1"></a><span class="co"># Define custom goodness-of-fit map</span></span>
<span id="cb21-38"><a href="#cb21-38" aria-hidden="true" tabindex="-1"></a>gof_map_plm <span class="ot">&lt;-</span> <span class="fu">tribble</span>(</span>
<span id="cb21-39"><a href="#cb21-39" aria-hidden="true" tabindex="-1"></a>  <span class="sc">~</span>raw, <span class="sc">~</span>clean, <span class="sc">~</span>fmt,</span>
<span id="cb21-40"><a href="#cb21-40" aria-hidden="true" tabindex="-1"></a>  <span class="st">"nobs"</span>, <span class="st">"Num.Obs."</span>, <span class="dv">0</span>,</span>
<span id="cb21-41"><a href="#cb21-41" aria-hidden="true" tabindex="-1"></a>  <span class="st">"r.squared"</span>, <span class="st">"R²"</span>, <span class="dv">3</span>,</span>
<span id="cb21-42"><a href="#cb21-42" aria-hidden="true" tabindex="-1"></a>  <span class="st">"adj.r.squared"</span>, <span class="st">"Adj.R²"</span>, <span class="dv">3</span></span>
<span id="cb21-43"><a href="#cb21-43" aria-hidden="true" tabindex="-1"></a>)</span>
<span id="cb21-44"><a href="#cb21-44" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb21-45"><a href="#cb21-45" aria-hidden="true" tabindex="-1"></a><span class="co"># Create modelsummary table with Organization &amp; Environment style</span></span>
<span id="cb21-46"><a href="#cb21-46" aria-hidden="true" tabindex="-1"></a>plm_p3_table <span class="ot">&lt;-</span> <span class="fu">modelsummary</span>(</span>
<span id="cb21-47"><a href="#cb21-47" aria-hidden="true" tabindex="-1"></a>  <span class="fu">list</span>(<span class="st">"Two-way FE"</span> <span class="ot">=</span> p3plm1, <span class="st">"Individual FE"</span> <span class="ot">=</span> p3plm2, <span class="st">"Interaction Model"</span> <span class="ot">=</span> p3plm3),</span>
<span id="cb21-48"><a href="#cb21-48" aria-hidden="true" tabindex="-1"></a>  <span class="at">output =</span> <span class="st">"gt"</span>,</span>
<span id="cb21-49"><a href="#cb21-49" aria-hidden="true" tabindex="-1"></a>  <span class="at">stars =</span> <span class="fu">c</span>(<span class="st">'*'</span> <span class="ot">=</span> .<span class="dv">1</span>, <span class="st">'**'</span> <span class="ot">=</span> .<span class="dv">05</span>, <span class="st">'***'</span> <span class="ot">=</span> .<span class="dv">01</span>),</span>
<span id="cb21-50"><a href="#cb21-50" aria-hidden="true" tabindex="-1"></a>  <span class="at">coef_map =</span> coef_map_plm,</span>
<span id="cb21-51"><a href="#cb21-51" aria-hidden="true" tabindex="-1"></a>  <span class="at">gof_map =</span> gof_map_plm,</span>
<span id="cb21-52"><a href="#cb21-52" aria-hidden="true" tabindex="-1"></a>  <span class="at">title =</span> <span class="st">"Table B6. Fixed Effects Models: Political Connections"</span>,</span>
<span id="cb21-53"><a href="#cb21-53" aria-hidden="true" tabindex="-1"></a>  <span class="at">notes =</span> <span class="fu">list</span>(<span class="st">"Standard errors in parentheses."</span>,</span>
<span id="cb21-54"><a href="#cb21-54" aria-hidden="true" tabindex="-1"></a>              <span class="st">"* p &lt; 0.1, ** p &lt; 0.05, *** p &lt; 0.01"</span>),</span>
<span id="cb21-55"><a href="#cb21-55" aria-hidden="true" tabindex="-1"></a>  <span class="at">fmt =</span> <span class="dv">3</span>,</span>
<span id="cb21-56"><a href="#cb21-56" aria-hidden="true" tabindex="-1"></a>  <span class="at">estimate =</span> <span class="st">"{estimate}{stars}"</span>,</span>
<span id="cb21-57"><a href="#cb21-57" aria-hidden="true" tabindex="-1"></a>  <span class="at">statistic =</span> <span class="st">"({std.error})"</span>,</span>
<span id="cb21-58"><a href="#cb21-58" aria-hidden="true" tabindex="-1"></a>  <span class="at">add_rows =</span> fe_rows</span>
<span id="cb21-59"><a href="#cb21-59" aria-hidden="true" tabindex="-1"></a>) <span class="sc">%&gt;%</span></span>
<span id="cb21-60"><a href="#cb21-60" aria-hidden="true" tabindex="-1"></a>  <span class="fu">tab_style</span>(</span>
<span id="cb21-61"><a href="#cb21-61" aria-hidden="true" tabindex="-1"></a>    <span class="at">style =</span> <span class="fu">cell_text</span>(<span class="at">weight =</span> <span class="st">"bold"</span>),</span>
<span id="cb21-62"><a href="#cb21-62" aria-hidden="true" tabindex="-1"></a>    <span class="at">locations =</span> <span class="fu">cells_column_labels</span>()</span>
<span id="cb21-63"><a href="#cb21-63" aria-hidden="true" tabindex="-1"></a>  ) <span class="sc">%&gt;%</span></span>
<span id="cb21-64"><a href="#cb21-64" aria-hidden="true" tabindex="-1"></a>  <span class="fu">tab_style</span>(</span>
<span id="cb21-65"><a href="#cb21-65" aria-hidden="true" tabindex="-1"></a>    <span class="at">style =</span> <span class="fu">cell_text</span>(<span class="at">weight =</span> <span class="st">"bold"</span>),</span>
<span id="cb21-66"><a href="#cb21-66" aria-hidden="true" tabindex="-1"></a>    <span class="at">locations =</span> <span class="fu">cells_stub</span>()</span>
<span id="cb21-67"><a href="#cb21-67" aria-hidden="true" tabindex="-1"></a>  ) <span class="sc">%&gt;%</span></span>
<span id="cb21-68"><a href="#cb21-68" aria-hidden="true" tabindex="-1"></a>  <span class="fu">tab_options</span>(</span>
<span id="cb21-69"><a href="#cb21-69" aria-hidden="true" tabindex="-1"></a>    <span class="at">table.font.size =</span> <span class="dv">11</span>,</span>
<span id="cb21-70"><a href="#cb21-70" aria-hidden="true" tabindex="-1"></a>    <span class="at">heading.title.font.size =</span> <span class="dv">12</span>,</span>
<span id="cb21-71"><a href="#cb21-71" aria-hidden="true" tabindex="-1"></a>    <span class="at">heading.subtitle.font.size =</span> <span class="dv">11</span>,</span>
<span id="cb21-72"><a href="#cb21-72" aria-hidden="true" tabindex="-1"></a>    <span class="at">table.border.top.style =</span> <span class="st">"solid"</span>,</span>
<span id="cb21-73"><a href="#cb21-73" aria-hidden="true" tabindex="-1"></a>    <span class="at">table.border.bottom.style =</span> <span class="st">"solid"</span>,</span>
<span id="cb21-74"><a href="#cb21-74" aria-hidden="true" tabindex="-1"></a>    <span class="at">heading.border.bottom.style =</span> <span class="st">"solid"</span>,</span>
<span id="cb21-75"><a href="#cb21-75" aria-hidden="true" tabindex="-1"></a>    <span class="at">column_labels.border.top.style =</span> <span class="st">"solid"</span>,</span>
<span id="cb21-76"><a href="#cb21-76" aria-hidden="true" tabindex="-1"></a>    <span class="at">column_labels.border.bottom.style =</span> <span class="st">"solid"</span>,</span>
<span id="cb21-77"><a href="#cb21-77" aria-hidden="true" tabindex="-1"></a>    <span class="at">stub.border.style =</span> <span class="st">"solid"</span></span>
<span id="cb21-78"><a href="#cb21-78" aria-hidden="true" tabindex="-1"></a>  )</span>
<span id="cb21-79"><a href="#cb21-79" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb21-80"><a href="#cb21-80" aria-hidden="true" tabindex="-1"></a><span class="co"># Save to file</span></span>
<span id="cb21-81"><a href="#cb21-81" aria-hidden="true" tabindex="-1"></a><span class="fu">gtsave</span>(plm_p3_table, <span class="st">"B6_plm_p3_OE_style.html"</span>)</span>
<span id="cb21-82"><a href="#cb21-82" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(plm_p3_table)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>&lt;div id="gtmwusoocr" style="padding-left:0px;padding-right:0px;padding-top:10px;padding-bottom:10px;overflow-x:auto;overflow-y:auto;width:auto;height:auto;"&gt;
  &lt;style&gt;#gtmwusoocr table {
  font-family: system-ui, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#gtmwusoocr thead, #gtmwusoocr tbody, #gtmwusoocr tfoot, #gtmwusoocr tr, #gtmwusoocr td, #gtmwusoocr th {
  border-style: none;
}

#gtmwusoocr p {
  margin: 0;
  padding: 0;
}

#gtmwusoocr .gt_table {
  display: table;
  border-collapse: collapse;
  line-height: normal;
  margin-left: auto;
  margin-right: auto;
  color: #333333;
  font-size: 11px;
  font-weight: normal;
  font-style: normal;
  background-color: #FFFFFF;
  width: auto;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #A8A8A8;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #A8A8A8;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
}

#gtmwusoocr .gt_caption {
  padding-top: 4px;
  padding-bottom: 4px;
}

#gtmwusoocr .gt_title {
  color: #333333;
  font-size: 12px;
  font-weight: initial;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-color: #FFFFFF;
  border-bottom-width: 0;
}

#gtmwusoocr .gt_subtitle {
  color: #333333;
  font-size: 11px;
  font-weight: initial;
  padding-top: 3px;
  padding-bottom: 5px;
  padding-left: 5px;
  padding-right: 5px;
  border-top-color: #FFFFFF;
  border-top-width: 0;
}

#gtmwusoocr .gt_heading {
  background-color: #FFFFFF;
  text-align: center;
  border-bottom-color: #FFFFFF;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
}

#gtmwusoocr .gt_bottom_border {
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#gtmwusoocr .gt_col_headings {
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
}

#gtmwusoocr .gt_col_heading {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: normal;
  text-transform: inherit;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: bottom;
  padding-top: 5px;
  padding-bottom: 6px;
  padding-left: 5px;
  padding-right: 5px;
  overflow-x: hidden;
}

#gtmwusoocr .gt_column_spanner_outer {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: normal;
  text-transform: inherit;
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 4px;
  padding-right: 4px;
}

#gtmwusoocr .gt_column_spanner_outer:first-child {
  padding-left: 0;
}

#gtmwusoocr .gt_column_spanner_outer:last-child {
  padding-right: 0;
}

#gtmwusoocr .gt_column_spanner {
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  vertical-align: bottom;
  padding-top: 5px;
  padding-bottom: 5px;
  overflow-x: hidden;
  display: inline-block;
  width: 100%;
}

#gtmwusoocr .gt_spanner_row {
  border-bottom-style: hidden;
}

#gtmwusoocr .gt_group_heading {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: middle;
  text-align: left;
}

#gtmwusoocr .gt_empty_group_heading {
  padding: 0.5px;
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  vertical-align: middle;
}

#gtmwusoocr .gt_from_md &gt; :first-child {
  margin-top: 0;
}

#gtmwusoocr .gt_from_md &gt; :last-child {
  margin-bottom: 0;
}

#gtmwusoocr .gt_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  margin: 10px;
  border-top-style: solid;
  border-top-width: 1px;
  border-top-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: middle;
  overflow-x: hidden;
}

#gtmwusoocr .gt_stub {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-right-style: solid;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  padding-left: 5px;
  padding-right: 5px;
}

#gtmwusoocr .gt_stub_row_group {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-right-style: solid;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  padding-left: 5px;
  padding-right: 5px;
  vertical-align: top;
}

#gtmwusoocr .gt_row_group_first td {
  border-top-width: 2px;
}

#gtmwusoocr .gt_row_group_first th {
  border-top-width: 2px;
}

#gtmwusoocr .gt_summary_row {
  color: #333333;
  background-color: #FFFFFF;
  text-transform: inherit;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
}

#gtmwusoocr .gt_first_summary_row {
  border-top-style: solid;
  border-top-color: #D3D3D3;
}

#gtmwusoocr .gt_first_summary_row.thick {
  border-top-width: 2px;
}

#gtmwusoocr .gt_last_summary_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#gtmwusoocr .gt_grand_summary_row {
  color: #333333;
  background-color: #FFFFFF;
  text-transform: inherit;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
}

#gtmwusoocr .gt_first_grand_summary_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-top-style: double;
  border-top-width: 6px;
  border-top-color: #D3D3D3;
}

#gtmwusoocr .gt_last_grand_summary_row_top {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-style: double;
  border-bottom-width: 6px;
  border-bottom-color: #D3D3D3;
}

#gtmwusoocr .gt_striped {
  background-color: rgba(128, 128, 128, 0.05);
}

#gtmwusoocr .gt_table_body {
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#gtmwusoocr .gt_footnotes {
  color: #333333;
  background-color: #FFFFFF;
  border-bottom-style: none;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
}

#gtmwusoocr .gt_footnote {
  margin: 0px;
  font-size: 90%;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
}

#gtmwusoocr .gt_sourcenotes {
  color: #333333;
  background-color: #FFFFFF;
  border-bottom-style: none;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
}

#gtmwusoocr .gt_sourcenote {
  font-size: 90%;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
}

#gtmwusoocr .gt_left {
  text-align: left;
}

#gtmwusoocr .gt_center {
  text-align: center;
}

#gtmwusoocr .gt_right {
  text-align: right;
  font-variant-numeric: tabular-nums;
}

#gtmwusoocr .gt_font_normal {
  font-weight: normal;
}

#gtmwusoocr .gt_font_bold {
  font-weight: bold;
}

#gtmwusoocr .gt_font_italic {
  font-style: italic;
}

#gtmwusoocr .gt_super {
  font-size: 65%;
}

#gtmwusoocr .gt_footnote_marks {
  font-size: 75%;
  vertical-align: 0.4em;
  position: initial;
}

#gtmwusoocr .gt_asterisk {
  font-size: 100%;
  vertical-align: 0;
}

#gtmwusoocr .gt_indent_1 {
  text-indent: 5px;
}

#gtmwusoocr .gt_indent_2 {
  text-indent: 10px;
}

#gtmwusoocr .gt_indent_3 {
  text-indent: 15px;
}

#gtmwusoocr .gt_indent_4 {
  text-indent: 20px;
}

#gtmwusoocr .gt_indent_5 {
  text-indent: 25px;
}

#gtmwusoocr .katex-display {
  display: inline-flex !important;
  margin-bottom: 0.75em !important;
}

#gtmwusoocr div.Reactable &gt; div.rt-table &gt; div.rt-thead &gt; div.rt-tr.rt-tr-group-header &gt; div.rt-th-group:after {
  height: 0px !important;
}
&lt;/style&gt;
  &lt;table class="gt_table" data-quarto-disable-processing="false" data-quarto-bootstrap="false"&gt;
  &lt;caption&gt;Table B6. Fixed Effects Models: Political Connections&lt;/caption&gt;
  &lt;thead&gt;
    &lt;tr class="gt_col_headings"&gt;
      &lt;th class="gt_col_heading gt_columns_bottom_border gt_left" rowspan="1" colspan="1" style="font-weight: bold;" scope="col" id="a-"&gt; &lt;/th&gt;
      &lt;th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" style="font-weight: bold;" scope="col" id="Two-way-FE"&gt;Two-way FE&lt;/th&gt;
      &lt;th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" style="font-weight: bold;" scope="col" id="Individual-FE"&gt;Individual FE&lt;/th&gt;
      &lt;th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" style="font-weight: bold;" scope="col" id="Interaction-Model"&gt;Interaction Model&lt;/th&gt;
    &lt;/tr&gt;
  &lt;/thead&gt;
  &lt;tbody class="gt_table_body"&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Connections&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;-0.175***&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;-0.297***&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;(0.057)&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;(0.071)&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;After First Inspection&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;6.799***&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;6.051***&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;(0.200)&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;(0.252)&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;ESG Rating&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;0.151***&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;0.216***&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;0.217***&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;(0.016)&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;(0.017)&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;(0.017)&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Return on Assets&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;-0.374***&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;-0.332***&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;-0.326***&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;(0.088)&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;(0.096)&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;(0.096)&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Leverage&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;-0.002&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;-0.001&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;-0.001&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;(0.006)&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;(0.007)&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;(0.007)&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Log(Registered Capital)&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;0.553***&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;2.165***&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;2.218***&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left" style="border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #000000;"&gt;&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center" style="border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #000000;"&gt;(0.190)&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center" style="border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #000000;"&gt;(0.198)&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center" style="border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #000000;"&gt;(0.198)&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Num.Obs.&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;10777&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;10777&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;10777&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;R²&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;0.033&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;0.232&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;0.235&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Adj.R²&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;-0.129&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;0.104&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;0.107&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Firm fixed effects&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;Y&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Industry fixed effects&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;Y&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Province fixed effects&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;Y&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Year fixed effects&lt;/td&gt;
&lt;td headers="Two-way FE" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Individual FE" class="gt_row gt_center"&gt;N&lt;/td&gt;
&lt;td headers="Interaction Model" class="gt_row gt_center"&gt;N&lt;/td&gt;&lt;/tr&gt;
  &lt;/tbody&gt;
  &lt;tfoot class="gt_sourcenotes"&gt;
    &lt;tr&gt;
      &lt;td class="gt_sourcenote" colspan="4"&gt;Standard errors in parentheses.&lt;/td&gt;
    &lt;/tr&gt;
    &lt;tr&gt;
      &lt;td class="gt_sourcenote" colspan="4"&gt;* p &amp;lt; 0.1, ** p &amp;lt; 0.05, *** p &amp;lt; 0.01&lt;/td&gt;
    &lt;/tr&gt;
  &lt;/tfoot&gt;
  
&lt;/table&gt;
&lt;/div&gt;</code></pre>
</div>
</div>
</section>
<section id="p4-contrallocal-connection-continuous-1" class="level3">
<h3 class="anchored" data-anchor-id="p4-contrallocal-connection-continuous-1">P4 contral/local connection (continuous)</h3>
<div class="cell">
<div class="sourceCode cell-code" id="cb23"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb23-1"><a href="#cb23-1" aria-hidden="true" tabindex="-1"></a><span class="co">#P4: The effects of central and local political connection (continuous) and policy pressure on environmental information disclosure</span></span>
<span id="cb23-2"><a href="#cb23-2" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(plm)</span>
<span id="cb23-3"><a href="#cb23-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb23-4"><a href="#cb23-4" aria-hidden="true" tabindex="-1"></a>p4plm1 <span class="ot">&lt;-</span> <span class="fu">plm</span>(Environmental_Information_Disclosure <span class="sc">~</span> central_connection <span class="sc">+</span> ESG_Rate  <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> <span class="fu">as.factor</span>(IndustryName)<span class="sc">+</span> <span class="fu">as.factor</span>(PROVINCE) <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span>ESG_Rate, <span class="at">data=</span>dta1,</span>
<span id="cb23-5"><a href="#cb23-5" aria-hidden="true" tabindex="-1"></a>            <span class="at">index=</span><span class="fu">c</span>(<span class="st">"Symbol"</span>, <span class="st">"EndYear"</span>),<span class="at">model=</span><span class="st">"within"</span>,<span class="at">effect=</span><span class="st">"twoways"</span>)</span>
<span id="cb23-6"><a href="#cb23-6" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb23-7"><a href="#cb23-7" aria-hidden="true" tabindex="-1"></a>p4plm2 <span class="ot">&lt;-</span> <span class="fu">plm</span>(Environmental_Information_Disclosure <span class="sc">~</span> central_connection <span class="sc">*</span> after_first_inspection <span class="sc">+</span> ESG_Rate  <span class="sc">+</span> <span class="fu">as.factor</span>(IndustryName)<span class="sc">+</span> <span class="fu">as.factor</span>(PROVINCE) <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span>ESG_Rate, <span class="at">data=</span>dta1,</span>
<span id="cb23-8"><a href="#cb23-8" aria-hidden="true" tabindex="-1"></a>            <span class="at">index=</span><span class="fu">c</span>(<span class="st">"Symbol"</span>),<span class="at">model=</span><span class="st">"within"</span>)</span>
<span id="cb23-9"><a href="#cb23-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb23-10"><a href="#cb23-10" aria-hidden="true" tabindex="-1"></a>p4plm3 <span class="ot">&lt;-</span> <span class="fu">plm</span>(Environmental_Information_Disclosure <span class="sc">~</span> local_connection <span class="sc">+</span> ESG_Rate  <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> <span class="fu">as.factor</span>(IndustryName)<span class="sc">+</span> <span class="fu">as.factor</span>(PROVINCE) <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span>ESG_Rate, <span class="at">data=</span>dta1,</span>
<span id="cb23-11"><a href="#cb23-11" aria-hidden="true" tabindex="-1"></a>            <span class="at">index=</span><span class="fu">c</span>(<span class="st">"Symbol"</span>, <span class="st">"EndYear"</span>),<span class="at">model=</span><span class="st">"within"</span>,<span class="at">effect=</span><span class="st">"twoways"</span>)</span>
<span id="cb23-12"><a href="#cb23-12" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb23-13"><a href="#cb23-13" aria-hidden="true" tabindex="-1"></a>p4plm4 <span class="ot">&lt;-</span> <span class="fu">plm</span>(Environmental_Information_Disclosure <span class="sc">~</span> local_connection <span class="sc">*</span> after_first_inspection <span class="sc">+</span> ESG_Rate  <span class="sc">+</span> <span class="fu">as.factor</span>(IndustryName)<span class="sc">+</span> <span class="fu">as.factor</span>(PROVINCE) <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span>ESG_Rate, <span class="at">data=</span>dta1,</span>
<span id="cb23-14"><a href="#cb23-14" aria-hidden="true" tabindex="-1"></a>            <span class="at">index=</span><span class="fu">c</span>(<span class="st">"Symbol"</span>),<span class="at">model=</span><span class="st">"within"</span>)</span>
<span id="cb23-15"><a href="#cb23-15" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb23-16"><a href="#cb23-16" aria-hidden="true" tabindex="-1"></a><span class="co"># Create Organization &amp; Environment style table for PLM central/local models using modelsummary</span></span>
<span id="cb23-17"><a href="#cb23-17" aria-hidden="true" tabindex="-1"></a><span class="co"># Define coefficient names for central/local connections</span></span>
<span id="cb23-18"><a href="#cb23-18" aria-hidden="true" tabindex="-1"></a>coef_map_plm_p4 <span class="ot">&lt;-</span> <span class="fu">c</span>(</span>
<span id="cb23-19"><a href="#cb23-19" aria-hidden="true" tabindex="-1"></a>  <span class="st">"Age"</span> <span class="ot">=</span> <span class="st">"Age"</span>,</span>
<span id="cb23-20"><a href="#cb23-20" aria-hidden="true" tabindex="-1"></a>  <span class="st">"central_connection"</span> <span class="ot">=</span> <span class="st">"Central Connections"</span>,</span>
<span id="cb23-21"><a href="#cb23-21" aria-hidden="true" tabindex="-1"></a>  <span class="st">"local_connection"</span> <span class="ot">=</span> <span class="st">"Local Connections"</span>,</span>
<span id="cb23-22"><a href="#cb23-22" aria-hidden="true" tabindex="-1"></a>  <span class="st">"after_first_inspection"</span> <span class="ot">=</span> <span class="st">"After First Inspection"</span>,</span>
<span id="cb23-23"><a href="#cb23-23" aria-hidden="true" tabindex="-1"></a>  <span class="st">"ESG_Rate"</span> <span class="ot">=</span> <span class="st">"ESG Rating"</span>,</span>
<span id="cb23-24"><a href="#cb23-24" aria-hidden="true" tabindex="-1"></a>  <span class="st">"ROA"</span> <span class="ot">=</span> <span class="st">"Return on Assets"</span>,</span>
<span id="cb23-25"><a href="#cb23-25" aria-hidden="true" tabindex="-1"></a>  <span class="st">"Leverage"</span> <span class="ot">=</span> <span class="st">"Leverage"</span>,</span>
<span id="cb23-26"><a href="#cb23-26" aria-hidden="true" tabindex="-1"></a>  <span class="st">"RegisterCapital_log"</span> <span class="ot">=</span> <span class="st">"Log(Registered Capital)"</span>,</span>
<span id="cb23-27"><a href="#cb23-27" aria-hidden="true" tabindex="-1"></a>  <span class="st">"central_connection:after_first_inspection"</span> <span class="ot">=</span> <span class="st">"Central Connections × After Inspection"</span>,</span>
<span id="cb23-28"><a href="#cb23-28" aria-hidden="true" tabindex="-1"></a>  <span class="st">"local_connection:after_first_inspection"</span> <span class="ot">=</span> <span class="st">"Local Connections × After Inspection"</span></span>
<span id="cb23-29"><a href="#cb23-29" aria-hidden="true" tabindex="-1"></a>)</span>
<span id="cb23-30"><a href="#cb23-30" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb23-31"><a href="#cb23-31" aria-hidden="true" tabindex="-1"></a><span class="co"># Create additional rows for fixed effects information for P4 models</span></span>
<span id="cb23-32"><a href="#cb23-32" aria-hidden="true" tabindex="-1"></a>fe_rows_p4 <span class="ot">&lt;-</span> <span class="fu">tribble</span>(</span>
<span id="cb23-33"><a href="#cb23-33" aria-hidden="true" tabindex="-1"></a>  <span class="sc">~</span>term, <span class="sc">~</span><span class="st">`</span><span class="at">Central (Two-way)</span><span class="st">`</span>, <span class="sc">~</span><span class="st">`</span><span class="at">Central × Inspection</span><span class="st">`</span>, <span class="sc">~</span><span class="st">`</span><span class="at">Local (Two-way)</span><span class="st">`</span>, <span class="sc">~</span><span class="st">`</span><span class="at">Local × Inspection</span><span class="st">`</span>,</span>
<span id="cb23-34"><a href="#cb23-34" aria-hidden="true" tabindex="-1"></a>  <span class="st">"Firm fixed effects"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>,</span>
<span id="cb23-35"><a href="#cb23-35" aria-hidden="true" tabindex="-1"></a>  <span class="st">"Industry fixed effects"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>,</span>
<span id="cb23-36"><a href="#cb23-36" aria-hidden="true" tabindex="-1"></a>  <span class="st">"Province fixed effects"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>, <span class="st">"Y"</span>,</span>
<span id="cb23-37"><a href="#cb23-37" aria-hidden="true" tabindex="-1"></a>  <span class="st">"Year fixed effects"</span>, <span class="st">"Y"</span>, <span class="st">"N"</span>, <span class="st">"Y"</span>, <span class="st">"N"</span></span>
<span id="cb23-38"><a href="#cb23-38" aria-hidden="true" tabindex="-1"></a>)</span>
<span id="cb23-39"><a href="#cb23-39" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb23-40"><a href="#cb23-40" aria-hidden="true" tabindex="-1"></a><span class="co"># Create modelsummary table with Organization &amp; Environment style</span></span>
<span id="cb23-41"><a href="#cb23-41" aria-hidden="true" tabindex="-1"></a>plm_p4_table <span class="ot">&lt;-</span> <span class="fu">modelsummary</span>(</span>
<span id="cb23-42"><a href="#cb23-42" aria-hidden="true" tabindex="-1"></a>  <span class="fu">list</span>(<span class="st">"Central (Two-way)"</span> <span class="ot">=</span> p4plm1, <span class="st">"Central × Inspection"</span> <span class="ot">=</span> p4plm2,</span>
<span id="cb23-43"><a href="#cb23-43" aria-hidden="true" tabindex="-1"></a>       <span class="st">"Local (Two-way)"</span> <span class="ot">=</span> p4plm3, <span class="st">"Local × Inspection"</span> <span class="ot">=</span> p4plm4),</span>
<span id="cb23-44"><a href="#cb23-44" aria-hidden="true" tabindex="-1"></a>  <span class="at">output =</span> <span class="st">"gt"</span>,</span>
<span id="cb23-45"><a href="#cb23-45" aria-hidden="true" tabindex="-1"></a>  <span class="at">stars =</span> <span class="fu">c</span>(<span class="st">'*'</span> <span class="ot">=</span> .<span class="dv">1</span>, <span class="st">'**'</span> <span class="ot">=</span> .<span class="dv">05</span>, <span class="st">'***'</span> <span class="ot">=</span> .<span class="dv">01</span>),</span>
<span id="cb23-46"><a href="#cb23-46" aria-hidden="true" tabindex="-1"></a>  <span class="at">coef_map =</span> coef_map_plm_p4,</span>
<span id="cb23-47"><a href="#cb23-47" aria-hidden="true" tabindex="-1"></a>  <span class="at">gof_map =</span> gof_map_plm,</span>
<span id="cb23-48"><a href="#cb23-48" aria-hidden="true" tabindex="-1"></a>  <span class="at">title =</span> <span class="st">"Table B7. Fixed Effects Models: Central vs Local Political Connections"</span>,</span>
<span id="cb23-49"><a href="#cb23-49" aria-hidden="true" tabindex="-1"></a>  <span class="at">notes =</span> <span class="fu">list</span>(<span class="st">"Standard errors in parentheses."</span>,</span>
<span id="cb23-50"><a href="#cb23-50" aria-hidden="true" tabindex="-1"></a>              <span class="st">"* p &lt; 0.1, ** p &lt; 0.05, *** p &lt; 0.01"</span>),</span>
<span id="cb23-51"><a href="#cb23-51" aria-hidden="true" tabindex="-1"></a>  <span class="at">fmt =</span> <span class="dv">3</span>,</span>
<span id="cb23-52"><a href="#cb23-52" aria-hidden="true" tabindex="-1"></a>  <span class="at">estimate =</span> <span class="st">"{estimate}{stars}"</span>,</span>
<span id="cb23-53"><a href="#cb23-53" aria-hidden="true" tabindex="-1"></a>  <span class="at">statistic =</span> <span class="st">"({std.error})"</span>,</span>
<span id="cb23-54"><a href="#cb23-54" aria-hidden="true" tabindex="-1"></a>  <span class="at">add_rows =</span> fe_rows_p4</span>
<span id="cb23-55"><a href="#cb23-55" aria-hidden="true" tabindex="-1"></a>) <span class="sc">%&gt;%</span></span>
<span id="cb23-56"><a href="#cb23-56" aria-hidden="true" tabindex="-1"></a>  <span class="fu">tab_style</span>(</span>
<span id="cb23-57"><a href="#cb23-57" aria-hidden="true" tabindex="-1"></a>    <span class="at">style =</span> <span class="fu">cell_text</span>(<span class="at">weight =</span> <span class="st">"bold"</span>),</span>
<span id="cb23-58"><a href="#cb23-58" aria-hidden="true" tabindex="-1"></a>    <span class="at">locations =</span> <span class="fu">cells_column_labels</span>()</span>
<span id="cb23-59"><a href="#cb23-59" aria-hidden="true" tabindex="-1"></a>  ) <span class="sc">%&gt;%</span></span>
<span id="cb23-60"><a href="#cb23-60" aria-hidden="true" tabindex="-1"></a>  <span class="fu">tab_style</span>(</span>
<span id="cb23-61"><a href="#cb23-61" aria-hidden="true" tabindex="-1"></a>    <span class="at">style =</span> <span class="fu">cell_text</span>(<span class="at">weight =</span> <span class="st">"bold"</span>),</span>
<span id="cb23-62"><a href="#cb23-62" aria-hidden="true" tabindex="-1"></a>    <span class="at">locations =</span> <span class="fu">cells_stub</span>()</span>
<span id="cb23-63"><a href="#cb23-63" aria-hidden="true" tabindex="-1"></a>  ) <span class="sc">%&gt;%</span></span>
<span id="cb23-64"><a href="#cb23-64" aria-hidden="true" tabindex="-1"></a>  <span class="fu">tab_options</span>(</span>
<span id="cb23-65"><a href="#cb23-65" aria-hidden="true" tabindex="-1"></a>    <span class="at">table.font.size =</span> <span class="dv">11</span>,</span>
<span id="cb23-66"><a href="#cb23-66" aria-hidden="true" tabindex="-1"></a>    <span class="at">heading.title.font.size =</span> <span class="dv">12</span>,</span>
<span id="cb23-67"><a href="#cb23-67" aria-hidden="true" tabindex="-1"></a>    <span class="at">heading.subtitle.font.size =</span> <span class="dv">11</span>,</span>
<span id="cb23-68"><a href="#cb23-68" aria-hidden="true" tabindex="-1"></a>    <span class="at">table.border.top.style =</span> <span class="st">"solid"</span>,</span>
<span id="cb23-69"><a href="#cb23-69" aria-hidden="true" tabindex="-1"></a>    <span class="at">table.border.bottom.style =</span> <span class="st">"solid"</span>,</span>
<span id="cb23-70"><a href="#cb23-70" aria-hidden="true" tabindex="-1"></a>    <span class="at">heading.border.bottom.style =</span> <span class="st">"solid"</span>,</span>
<span id="cb23-71"><a href="#cb23-71" aria-hidden="true" tabindex="-1"></a>    <span class="at">column_labels.border.top.style =</span> <span class="st">"solid"</span>,</span>
<span id="cb23-72"><a href="#cb23-72" aria-hidden="true" tabindex="-1"></a>    <span class="at">column_labels.border.bottom.style =</span> <span class="st">"solid"</span>,</span>
<span id="cb23-73"><a href="#cb23-73" aria-hidden="true" tabindex="-1"></a>    <span class="at">stub.border.style =</span> <span class="st">"solid"</span></span>
<span id="cb23-74"><a href="#cb23-74" aria-hidden="true" tabindex="-1"></a>  )</span>
<span id="cb23-75"><a href="#cb23-75" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb23-76"><a href="#cb23-76" aria-hidden="true" tabindex="-1"></a><span class="co"># Save to file</span></span>
<span id="cb23-77"><a href="#cb23-77" aria-hidden="true" tabindex="-1"></a><span class="fu">gtsave</span>(plm_p4_table, <span class="st">"B7_plm_p4_OE_style.html"</span>)</span>
<span id="cb23-78"><a href="#cb23-78" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(plm_p4_table)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>&lt;div id="vvpkqfozrc" style="padding-left:0px;padding-right:0px;padding-top:10px;padding-bottom:10px;overflow-x:auto;overflow-y:auto;width:auto;height:auto;"&gt;
  &lt;style&gt;#vvpkqfozrc table {
  font-family: system-ui, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#vvpkqfozrc thead, #vvpkqfozrc tbody, #vvpkqfozrc tfoot, #vvpkqfozrc tr, #vvpkqfozrc td, #vvpkqfozrc th {
  border-style: none;
}

#vvpkqfozrc p {
  margin: 0;
  padding: 0;
}

#vvpkqfozrc .gt_table {
  display: table;
  border-collapse: collapse;
  line-height: normal;
  margin-left: auto;
  margin-right: auto;
  color: #333333;
  font-size: 11px;
  font-weight: normal;
  font-style: normal;
  background-color: #FFFFFF;
  width: auto;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #A8A8A8;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #A8A8A8;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
}

#vvpkqfozrc .gt_caption {
  padding-top: 4px;
  padding-bottom: 4px;
}

#vvpkqfozrc .gt_title {
  color: #333333;
  font-size: 12px;
  font-weight: initial;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-color: #FFFFFF;
  border-bottom-width: 0;
}

#vvpkqfozrc .gt_subtitle {
  color: #333333;
  font-size: 11px;
  font-weight: initial;
  padding-top: 3px;
  padding-bottom: 5px;
  padding-left: 5px;
  padding-right: 5px;
  border-top-color: #FFFFFF;
  border-top-width: 0;
}

#vvpkqfozrc .gt_heading {
  background-color: #FFFFFF;
  text-align: center;
  border-bottom-color: #FFFFFF;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
}

#vvpkqfozrc .gt_bottom_border {
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#vvpkqfozrc .gt_col_headings {
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
}

#vvpkqfozrc .gt_col_heading {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: normal;
  text-transform: inherit;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: bottom;
  padding-top: 5px;
  padding-bottom: 6px;
  padding-left: 5px;
  padding-right: 5px;
  overflow-x: hidden;
}

#vvpkqfozrc .gt_column_spanner_outer {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: normal;
  text-transform: inherit;
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 4px;
  padding-right: 4px;
}

#vvpkqfozrc .gt_column_spanner_outer:first-child {
  padding-left: 0;
}

#vvpkqfozrc .gt_column_spanner_outer:last-child {
  padding-right: 0;
}

#vvpkqfozrc .gt_column_spanner {
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  vertical-align: bottom;
  padding-top: 5px;
  padding-bottom: 5px;
  overflow-x: hidden;
  display: inline-block;
  width: 100%;
}

#vvpkqfozrc .gt_spanner_row {
  border-bottom-style: hidden;
}

#vvpkqfozrc .gt_group_heading {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: middle;
  text-align: left;
}

#vvpkqfozrc .gt_empty_group_heading {
  padding: 0.5px;
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  vertical-align: middle;
}

#vvpkqfozrc .gt_from_md &gt; :first-child {
  margin-top: 0;
}

#vvpkqfozrc .gt_from_md &gt; :last-child {
  margin-bottom: 0;
}

#vvpkqfozrc .gt_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  margin: 10px;
  border-top-style: solid;
  border-top-width: 1px;
  border-top-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 1px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 1px;
  border-right-color: #D3D3D3;
  vertical-align: middle;
  overflow-x: hidden;
}

#vvpkqfozrc .gt_stub {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-right-style: solid;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  padding-left: 5px;
  padding-right: 5px;
}

#vvpkqfozrc .gt_stub_row_group {
  color: #333333;
  background-color: #FFFFFF;
  font-size: 100%;
  font-weight: initial;
  text-transform: inherit;
  border-right-style: solid;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
  padding-left: 5px;
  padding-right: 5px;
  vertical-align: top;
}

#vvpkqfozrc .gt_row_group_first td {
  border-top-width: 2px;
}

#vvpkqfozrc .gt_row_group_first th {
  border-top-width: 2px;
}

#vvpkqfozrc .gt_summary_row {
  color: #333333;
  background-color: #FFFFFF;
  text-transform: inherit;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
}

#vvpkqfozrc .gt_first_summary_row {
  border-top-style: solid;
  border-top-color: #D3D3D3;
}

#vvpkqfozrc .gt_first_summary_row.thick {
  border-top-width: 2px;
}

#vvpkqfozrc .gt_last_summary_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#vvpkqfozrc .gt_grand_summary_row {
  color: #333333;
  background-color: #FFFFFF;
  text-transform: inherit;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
}

#vvpkqfozrc .gt_first_grand_summary_row {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-top-style: double;
  border-top-width: 6px;
  border-top-color: #D3D3D3;
}

#vvpkqfozrc .gt_last_grand_summary_row_top {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
  border-bottom-style: double;
  border-bottom-width: 6px;
  border-bottom-color: #D3D3D3;
}

#vvpkqfozrc .gt_striped {
  background-color: rgba(128, 128, 128, 0.05);
}

#vvpkqfozrc .gt_table_body {
  border-top-style: solid;
  border-top-width: 2px;
  border-top-color: #D3D3D3;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
}

#vvpkqfozrc .gt_footnotes {
  color: #333333;
  background-color: #FFFFFF;
  border-bottom-style: none;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
}

#vvpkqfozrc .gt_footnote {
  margin: 0px;
  font-size: 90%;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
}

#vvpkqfozrc .gt_sourcenotes {
  color: #333333;
  background-color: #FFFFFF;
  border-bottom-style: none;
  border-bottom-width: 2px;
  border-bottom-color: #D3D3D3;
  border-left-style: none;
  border-left-width: 2px;
  border-left-color: #D3D3D3;
  border-right-style: none;
  border-right-width: 2px;
  border-right-color: #D3D3D3;
}

#vvpkqfozrc .gt_sourcenote {
  font-size: 90%;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 5px;
  padding-right: 5px;
}

#vvpkqfozrc .gt_left {
  text-align: left;
}

#vvpkqfozrc .gt_center {
  text-align: center;
}

#vvpkqfozrc .gt_right {
  text-align: right;
  font-variant-numeric: tabular-nums;
}

#vvpkqfozrc .gt_font_normal {
  font-weight: normal;
}

#vvpkqfozrc .gt_font_bold {
  font-weight: bold;
}

#vvpkqfozrc .gt_font_italic {
  font-style: italic;
}

#vvpkqfozrc .gt_super {
  font-size: 65%;
}

#vvpkqfozrc .gt_footnote_marks {
  font-size: 75%;
  vertical-align: 0.4em;
  position: initial;
}

#vvpkqfozrc .gt_asterisk {
  font-size: 100%;
  vertical-align: 0;
}

#vvpkqfozrc .gt_indent_1 {
  text-indent: 5px;
}

#vvpkqfozrc .gt_indent_2 {
  text-indent: 10px;
}

#vvpkqfozrc .gt_indent_3 {
  text-indent: 15px;
}

#vvpkqfozrc .gt_indent_4 {
  text-indent: 20px;
}

#vvpkqfozrc .gt_indent_5 {
  text-indent: 25px;
}

#vvpkqfozrc .katex-display {
  display: inline-flex !important;
  margin-bottom: 0.75em !important;
}

#vvpkqfozrc div.Reactable &gt; div.rt-table &gt; div.rt-thead &gt; div.rt-tr.rt-tr-group-header &gt; div.rt-th-group:after {
  height: 0px !important;
}
&lt;/style&gt;
  &lt;table class="gt_table" data-quarto-disable-processing="false" data-quarto-bootstrap="false"&gt;
  &lt;caption&gt;Table B7. Fixed Effects Models: Central vs Local Political Connections&lt;/caption&gt;
  &lt;thead&gt;
    &lt;tr class="gt_col_headings"&gt;
      &lt;th class="gt_col_heading gt_columns_bottom_border gt_left" rowspan="1" colspan="1" style="font-weight: bold;" scope="col" id="a-"&gt; &lt;/th&gt;
      &lt;th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" style="font-weight: bold;" scope="col" id="Central-(Two-way)"&gt;Central (Two-way)&lt;/th&gt;
      &lt;th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" style="font-weight: bold;" scope="col" id="Central-×-Inspection"&gt;Central × Inspection&lt;/th&gt;
      &lt;th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" style="font-weight: bold;" scope="col" id="Local-(Two-way)"&gt;Local (Two-way)&lt;/th&gt;
      &lt;th class="gt_col_heading gt_columns_bottom_border gt_center" rowspan="1" colspan="1" style="font-weight: bold;" scope="col" id="Local-×-Inspection"&gt;Local × Inspection&lt;/th&gt;
    &lt;/tr&gt;
  &lt;/thead&gt;
  &lt;tbody class="gt_table_body"&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Central Connections&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;-0.261&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;-0.873***&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;(0.187)&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;(0.244)&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Local Connections&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;-0.186***&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;-0.300***&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;(0.063)&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;(0.080)&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;After First Inspection&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;6.481***&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;6.154***&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;(0.212)&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;(0.250)&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;ESG Rating&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;0.151***&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;0.216***&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;0.150***&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;0.217***&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;(0.016)&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;(0.017)&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;(0.016)&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;(0.017)&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Return on Assets&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;-0.375***&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;-0.329***&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;-0.374***&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;-0.327***&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;(0.088)&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;(0.096)&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;(0.088)&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;(0.096)&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Leverage&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;-0.002&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;-0.001&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;-0.002&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;-0.001&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;(0.006)&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;(0.007)&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;(0.006)&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;(0.007)&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Log(Registered Capital)&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;0.546***&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;2.186***&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;0.551***&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;2.211***&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;(0.190)&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;(0.198)&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;(0.190)&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;(0.198)&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Central Connections × After Inspection&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;1.285***&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;(0.304)&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Local Connections × After Inspection&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;0.353***&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left" style="border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #000000;"&gt;&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center" style="border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #000000;"&gt;&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center" style="border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #000000;"&gt;&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center" style="border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #000000;"&gt;&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center" style="border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #000000;"&gt;(0.082)&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Num.Obs.&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;10777&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;10777&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;10777&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;10777&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;R²&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;0.032&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;0.234&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;0.033&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;0.234&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Adj.R²&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;-0.130&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;0.106&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;-0.129&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;0.106&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Firm fixed effects&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;Y&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Industry fixed effects&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;Y&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Province fixed effects&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;Y&lt;/td&gt;&lt;/tr&gt;
    &lt;tr&gt;&lt;td headers=" " class="gt_row gt_left"&gt;Year fixed effects&lt;/td&gt;
&lt;td headers="Central (Two-way)" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Central × Inspection" class="gt_row gt_center"&gt;N&lt;/td&gt;
&lt;td headers="Local (Two-way)" class="gt_row gt_center"&gt;Y&lt;/td&gt;
&lt;td headers="Local × Inspection" class="gt_row gt_center"&gt;N&lt;/td&gt;&lt;/tr&gt;
  &lt;/tbody&gt;
  &lt;tfoot class="gt_sourcenotes"&gt;
    &lt;tr&gt;
      &lt;td class="gt_sourcenote" colspan="5"&gt;Standard errors in parentheses.&lt;/td&gt;
    &lt;/tr&gt;
    &lt;tr&gt;
      &lt;td class="gt_sourcenote" colspan="5"&gt;* p &amp;lt; 0.1, ** p &amp;lt; 0.05, *** p &amp;lt; 0.01&lt;/td&gt;
    &lt;/tr&gt;
  &lt;/tfoot&gt;
  
&lt;/table&gt;
&lt;/div&gt;</code></pre>
</div>
</div>
</section>
</section>
</section>

</main>
<!-- /main column -->
<script id="quarto-html-after-body" type="application/javascript">
window.document.addEventListener("DOMContentLoaded", function (event) {
  const toggleBodyColorMode = (bsSheetEl) => {
    const mode = bsSheetEl.getAttribute("data-mode");
    const bodyEl = window.document.querySelector("body");
    if (mode === "dark") {
      bodyEl.classList.add("quarto-dark");
      bodyEl.classList.remove("quarto-light");
    } else {
      bodyEl.classList.add("quarto-light");
      bodyEl.classList.remove("quarto-dark");
    }
  }
  const toggleBodyColorPrimary = () => {
    const bsSheetEl = window.document.querySelector("link#quarto-bootstrap");
    if (bsSheetEl) {
      toggleBodyColorMode(bsSheetEl);
    }
  }
  toggleBodyColorPrimary();  
  const icon = "";
  const anchorJS = new window.AnchorJS();
  anchorJS.options = {
    placement: 'right',
    icon: icon
  };
  anchorJS.add('.anchored');
  const isCodeAnnotation = (el) => {
    for (const clz of el.classList) {
      if (clz.startsWith('code-annotation-')) {                     
        return true;
      }
    }
    return false;
  }
  const onCopySuccess = function(e) {
    // button target
    const button = e.trigger;
    // don't keep focus
    button.blur();
    // flash "checked"
    button.classList.add('code-copy-button-checked');
    var currentTitle = button.getAttribute("title");
    button.setAttribute("title", "Copied!");
    let tooltip;
    if (window.bootstrap) {
      button.setAttribute("data-bs-toggle", "tooltip");
      button.setAttribute("data-bs-placement", "left");
      button.setAttribute("data-bs-title", "Copied!");
      tooltip = new bootstrap.Tooltip(button, 
        { trigger: "manual", 
          customClass: "code-copy-button-tooltip",
          offset: [0, -8]});
      tooltip.show();    
    }
    setTimeout(function() {
      if (tooltip) {
        tooltip.hide();
        button.removeAttribute("data-bs-title");
        button.removeAttribute("data-bs-toggle");
        button.removeAttribute("data-bs-placement");
      }
      button.setAttribute("title", currentTitle);
      button.classList.remove('code-copy-button-checked');
    }, 1000);
    // clear code selection
    e.clearSelection();
  }
  const getTextToCopy = function(trigger) {
      const codeEl = trigger.previousElementSibling.cloneNode(true);
      for (const childEl of codeEl.children) {
        if (isCodeAnnotation(childEl)) {
          childEl.remove();
        }
      }
      return codeEl.innerText;
  }
  const clipboard = new window.ClipboardJS('.code-copy-button:not([data-in-quarto-modal])', {
    text: getTextToCopy
  });
  clipboard.on('success', onCopySuccess);
  if (window.document.getElementById('quarto-embedded-source-code-modal')) {
    const clipboardModal = new window.ClipboardJS('.code-copy-button[data-in-quarto-modal]', {
      text: getTextToCopy,
      container: window.document.getElementById('quarto-embedded-source-code-modal')
    });
    clipboardModal.on('success', onCopySuccess);
  }
    var localhostRegex = new RegExp(/^(?:http|https):\/\/localhost\:?[0-9]*\//);
    var mailtoRegex = new RegExp(/^mailto:/);
      var filterRegex = new RegExp('/' + window.location.host + '/');
    var isInternal = (href) => {
        return filterRegex.test(href) || localhostRegex.test(href) || mailtoRegex.test(href);
    }
    // Inspect non-navigation links and adorn them if external
 	var links = window.document.querySelectorAll('a[href]:not(.nav-link):not(.navbar-brand):not(.toc-action):not(.sidebar-link):not(.sidebar-item-toggle):not(.pagination-link):not(.no-external):not([aria-hidden]):not(.dropdown-item):not(.quarto-navigation-tool):not(.about-link)');
    for (var i=0; i<links.length; i++) {
      const link = links[i];
      if (!isInternal(link.href)) {
        // undo the damage that might have been done by quarto-nav.js in the case of
        // links that we want to consider external
        if (link.dataset.originalHref !== undefined) {
          link.href = link.dataset.originalHref;
        }
      }
    }
  function tippyHover(el, contentFn, onTriggerFn, onUntriggerFn) {
    const config = {
      allowHTML: true,
      maxWidth: 500,
      delay: 100,
      arrow: false,
      appendTo: function(el) {
          return el.parentElement;
      },
      interactive: true,
      interactiveBorder: 10,
      theme: 'quarto',
      placement: 'bottom-start',
    };
    if (contentFn) {
      config.content = contentFn;
    }
    if (onTriggerFn) {
      config.onTrigger = onTriggerFn;
    }
    if (onUntriggerFn) {
      config.onUntrigger = onUntriggerFn;
    }
    window.tippy(el, config); 
  }
  const noterefs = window.document.querySelectorAll('a[role="doc-noteref"]');
  for (var i=0; i<noterefs.length; i++) {
    const ref = noterefs[i];
    tippyHover(ref, function() {
      // use id or data attribute instead here
      let href = ref.getAttribute('data-footnote-href') || ref.getAttribute('href');
      try { href = new URL(href).hash; } catch {}
      const id = href.replace(/^#\/?/, "");
      const note = window.document.getElementById(id);
      if (note) {
        return note.innerHTML;
      } else {
        return "";
      }
    });
  }
  const xrefs = window.document.querySelectorAll('a.quarto-xref');
  const processXRef = (id, note) => {
    // Strip column container classes
    const stripColumnClz = (el) => {
      el.classList.remove("page-full", "page-columns");
      if (el.children) {
        for (const child of el.children) {
          stripColumnClz(child);
        }
      }
    }
    stripColumnClz(note)
    if (id === null || id.startsWith('sec-')) {
      // Special case sections, only their first couple elements
      const container = document.createElement("div");
      if (note.children && note.children.length > 2) {
        container.appendChild(note.children[0].cloneNode(true));
        for (let i = 1; i < note.children.length; i++) {
          const child = note.children[i];
          if (child.tagName === "P" && child.innerText === "") {
            continue;
          } else {
            container.appendChild(child.cloneNode(true));
            break;
          }
        }
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(container);
        }
        return container.innerHTML
      } else {
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(note);
        }
        return note.innerHTML;
      }
    } else {
      // Remove any anchor links if they are present
      const anchorLink = note.querySelector('a.anchorjs-link');
      if (anchorLink) {
        anchorLink.remove();
      }
      if (window.Quarto?.typesetMath) {
        window.Quarto.typesetMath(note);
      }
      if (note.classList.contains("callout")) {
        return note.outerHTML;
      } else {
        return note.innerHTML;
      }
    }
  }
  for (var i=0; i<xrefs.length; i++) {
    const xref = xrefs[i];
    tippyHover(xref, undefined, function(instance) {
      instance.disable();
      let url = xref.getAttribute('href');
      let hash = undefined; 
      if (url.startsWith('#')) {
        hash = url;
      } else {
        try { hash = new URL(url).hash; } catch {}
      }
      if (hash) {
        const id = hash.replace(/^#\/?/, "");
        const note = window.document.getElementById(id);
        if (note !== null) {
          try {
            const html = processXRef(id, note.cloneNode(true));
            instance.setContent(html);
          } finally {
            instance.enable();
            instance.show();
          }
        } else {
          // See if we can fetch this
          fetch(url.split('#')[0])
          .then(res => res.text())
          .then(html => {
            const parser = new DOMParser();
            const htmlDoc = parser.parseFromString(html, "text/html");
            const note = htmlDoc.getElementById(id);
            if (note !== null) {
              const html = processXRef(id, note);
              instance.setContent(html);
            } 
          }).finally(() => {
            instance.enable();
            instance.show();
          });
        }
      } else {
        // See if we can fetch a full url (with no hash to target)
        // This is a special case and we should probably do some content thinning / targeting
        fetch(url)
        .then(res => res.text())
        .then(html => {
          const parser = new DOMParser();
          const htmlDoc = parser.parseFromString(html, "text/html");
          const note = htmlDoc.querySelector('main.content');
          if (note !== null) {
            // This should only happen for chapter cross references
            // (since there is no id in the URL)
            // remove the first header
            if (note.children.length > 0 && note.children[0].tagName === "HEADER") {
              note.children[0].remove();
            }
            const html = processXRef(null, note);
            instance.setContent(html);
          } 
        }).finally(() => {
          instance.enable();
          instance.show();
        });
      }
    }, function(instance) {
    });
  }
      let selectedAnnoteEl;
      const selectorForAnnotation = ( cell, annotation) => {
        let cellAttr = 'data-code-cell="' + cell + '"';
        let lineAttr = 'data-code-annotation="' +  annotation + '"';
        const selector = 'span[' + cellAttr + '][' + lineAttr + ']';
        return selector;
      }
      const selectCodeLines = (annoteEl) => {
        const doc = window.document;
        const targetCell = annoteEl.getAttribute("data-target-cell");
        const targetAnnotation = annoteEl.getAttribute("data-target-annotation");
        const annoteSpan = window.document.querySelector(selectorForAnnotation(targetCell, targetAnnotation));
        const lines = annoteSpan.getAttribute("data-code-lines").split(",");
        const lineIds = lines.map((line) => {
          return targetCell + "-" + line;
        })
        let top = null;
        let height = null;
        let parent = null;
        if (lineIds.length > 0) {
            //compute the position of the single el (top and bottom and make a div)
            const el = window.document.getElementById(lineIds[0]);
            top = el.offsetTop;
            height = el.offsetHeight;
            parent = el.parentElement.parentElement;
          if (lineIds.length > 1) {
            const lastEl = window.document.getElementById(lineIds[lineIds.length - 1]);
            const bottom = lastEl.offsetTop + lastEl.offsetHeight;
            height = bottom - top;
          }
          if (top !== null && height !== null && parent !== null) {
            // cook up a div (if necessary) and position it 
            let div = window.document.getElementById("code-annotation-line-highlight");
            if (div === null) {
              div = window.document.createElement("div");
              div.setAttribute("id", "code-annotation-line-highlight");
              div.style.position = 'absolute';
              parent.appendChild(div);
            }
            div.style.top = top - 2 + "px";
            div.style.height = height + 4 + "px";
            div.style.left = 0;
            let gutterDiv = window.document.getElementById("code-annotation-line-highlight-gutter");
            if (gutterDiv === null) {
              gutterDiv = window.document.createElement("div");
              gutterDiv.setAttribute("id", "code-annotation-line-highlight-gutter");
              gutterDiv.style.position = 'absolute';
              const codeCell = window.document.getElementById(targetCell);
              const gutter = codeCell.querySelector('.code-annotation-gutter');
              gutter.appendChild(gutterDiv);
            }
            gutterDiv.style.top = top - 2 + "px";
            gutterDiv.style.height = height + 4 + "px";
          }
          selectedAnnoteEl = annoteEl;
        }
      };
      const unselectCodeLines = () => {
        const elementsIds = ["code-annotation-line-highlight", "code-annotation-line-highlight-gutter"];
        elementsIds.forEach((elId) => {
          const div = window.document.getElementById(elId);
          if (div) {
            div.remove();
          }
        });
        selectedAnnoteEl = undefined;
      };
        // Handle positioning of the toggle
    window.addEventListener(
      "resize",
      throttle(() => {
        elRect = undefined;
        if (selectedAnnoteEl) {
          selectCodeLines(selectedAnnoteEl);
        }
      }, 10)
    );
    function throttle(fn, ms) {
    let throttle = false;
    let timer;
      return (...args) => {
        if(!throttle) { // first call gets through
            fn.apply(this, args);
            throttle = true;
        } else { // all the others get throttled
            if(timer) clearTimeout(timer); // cancel #2
            timer = setTimeout(() => {
              fn.apply(this, args);
              timer = throttle = false;
            }, ms);
        }
      };
    }
      // Attach click handler to the DT
      const annoteDls = window.document.querySelectorAll('dt[data-target-cell]');
      for (const annoteDlNode of annoteDls) {
        annoteDlNode.addEventListener('click', (event) => {
          const clickedEl = event.target;
          if (clickedEl !== selectedAnnoteEl) {
            unselectCodeLines();
            const activeEl = window.document.querySelector('dt[data-target-cell].code-annotation-active');
            if (activeEl) {
              activeEl.classList.remove('code-annotation-active');
            }
            selectCodeLines(clickedEl);
            clickedEl.classList.add('code-annotation-active');
          } else {
            // Unselect the line
            unselectCodeLines();
            clickedEl.classList.remove('code-annotation-active');
          }
        });
      }
  const findCites = (el) => {
    const parentEl = el.parentElement;
    if (parentEl) {
      const cites = parentEl.dataset.cites;
      if (cites) {
        return {
          el,
          cites: cites.split(' ')
        };
      } else {
        return findCites(el.parentElement)
      }
    } else {
      return undefined;
    }
  };
  var bibliorefs = window.document.querySelectorAll('a[role="doc-biblioref"]');
  for (var i=0; i<bibliorefs.length; i++) {
    const ref = bibliorefs[i];
    const citeInfo = findCites(ref);
    if (citeInfo) {
      tippyHover(citeInfo.el, function() {
        var popup = window.document.createElement('div');
        citeInfo.cites.forEach(function(cite) {
          var citeDiv = window.document.createElement('div');
          citeDiv.classList.add('hanging-indent');
          citeDiv.classList.add('csl-entry');
          var biblioDiv = window.document.getElementById('ref-' + cite);
          if (biblioDiv) {
            citeDiv.innerHTML = biblioDiv.innerHTML;
          }
          popup.appendChild(citeDiv);
        });
        return popup.innerHTML;
      });
    }
  }
});
</script>
</div> <!-- /content -->




</body></html>